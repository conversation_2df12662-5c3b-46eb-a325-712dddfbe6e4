terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.28"
      configuration_aliases = [
        aws.use1,
      ]

    }
  }
}

locals {
  tf_dir                  = "terraform/production"
  undeployed_lambda_names = []
  eb_paths = {
    cdr_db           = "shaka/cdr_db"
    client_dashboard = "shaka/client_dashboard"
    nexus            = "shaka/nexus"
    simpnexus        = "shaka/nexus"
    sandbox          = "shaka/nexus"
    simpwebapp       = "shaka/simp-webapp"
  }
  lambda_paths = {
    build_notification             = "meta/build_notification"
    fetch_transatel_sftp_files     = "shaka/fetch_transatel_sftp_files"
    fetch_gamma_sftp_files         = "shaka/fetch_gamma_sftp_files"
    run_airtable_import            = "shaka/run_airtable_import"
    import_cdr_file                = "shaka/cdr_db"
    import_gamma_cdr_file          = "shaka/cdr_db"
    gamma_alerts                   = "shaka/cdr_db"
    check_for_cdr_gaps             = "shaka/cdr_db"
    cleanup_cdr_files              = "shaka/cdr_db"
    run_provider_actions           = "shaka/cdr_db"
    run_poller                     = "shaka/cdr_db"
    provider_specific_cdr_received = "shaka/cdr_db"
    provider_agnostic_cdr_received = "shaka/cdr_db"
    bar_data_for_dangerous_users   = "shaka/nexus"
    slack_debug                    = "shaka/nexus"
    plan_change                    = "shaka/nexus"
  }
  lambda_names = concat([for name, v in var.lambdas : name], local.undeployed_lambda_names)
  eb_names     = [for name, v in var.elastic_beanstalks : name]
}



variable "lambdas" {
  description = "Map of lambdas to deploy. "
  type        = map(any)

  default = {
    build_notification = {
      lambda_function_name = "prod-pipeline-notifications-slack-lambda"
      treat_missing_data   = "notBreaching"
    },
    fetch_transatel_sftp_files = {
      lambda_function_name = "prod-fetch-transatel-sftp-files"
    },
    fetch_gamma_sftp_files = {
      lambda_function_name = "prod-fetch-gamma-sftp-files"
    },
    run_airtable_import = {
      lambda_function_name = "prod-run-airtable-import"
    },
    import_cdr_file = {
      lambda_function_name = "prod-import-cdr-file"
    },
    import_gamma_cdr_file = {
      lambda_function_name = "prod-import-gamma-cdr-file"
    },
    gamma_alerts = {
      lambda_function_name = "prod-import-gamma-alert-file"
    },
    check_for_cdr_gaps = {
      lambda_function_name = "prod-check-for-cdr-gaps"
    },
    cleanup_cdr_files = {
      lambda_function_name = "prod-cleanup-cdr-files"
    },
    run_provider_actions = {
      lambda_function_name = "prod-run-provider-actions"
    },
    run_poller = {
      lambda_function_name = "prod-run-poller"
    },
    provider_specific_cdr_received = {
      lambda_function_name = "prod-provider-specific-cdr-converter"
    }
    provider_agnostic_cdr_received = {
      lambda_function_name = "prod-agnostic-cdr-receiver"
    }
    bar_data_for_dangerous_users = {
      lambda_function_name = "prod-bar-data-for-dangerous-users"
    },
    slack_debug = {
      lambda_function_name = "prod-slack-debug"
    }
    plan_change = {
      lambda_function_name = "prod-plan-change"
    }
  }
}

variable "frontends" {
  type = map(any)
  default = {
    client_dashboard_frontend = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-client-dashboard-frontend"
      config         = <<EOF
VITE_SHAKA_CLIENT_ID = 3
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_ENABLE_DEMO_FEATURES = 'true'
      EOF
    }
    six = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-six-dashboard-frontend"
      config         = <<EOF
VITE_SHAKA_CLIENT_ID = 4
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_ENABLE_DEMO_FEATURES = 'true'
      EOF
    }
    beta = {
      deploy_enabled = false
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-beta-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 5
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
          EOF
    }
    inovo = {
      deploy_enabled = false
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-inovo-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 7
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
          EOF
    }
    richmond = {
      deploy_enabled = false
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-richmond-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 8
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
          EOF
    }
    alpha = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-alpha-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 'alpha-X5523'
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
          EOF
    }
    yayzi = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-yayzi-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 10
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
    VITE_GOOGLE_TRACKING_ID = 'G-65T0E911PY'
      EOF
    }
    yayzi_beta = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-yayzi-beta-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 6
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
    VITE_GOOGLE_TRACKING_ID = 'G-65T0E911PY'
      EOF
    }
    itsi = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-itsi-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 11
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
      EOF
    }
    cove = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-cove-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 12
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
      EOF
    }
    perkfon = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-perkfon-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 'X2-PF'
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
      EOF
    }
    millwall = {
      deploy_enabled = true
      path           = "shaka/client_dashboard_frontend"
      bucket         = "prod-millwall-dashboard-frontend"
      config         = <<EOF
    VITE_SHAKA_CLIENT_ID = 's97x8733'
    VITE_BASE_URL = 'https://nexus.shaka.tel'
    VITE_ENABLE_DEMO_FEATURES = 'true'
      EOF
    }
    sample_subscriber_app = {
      deploy_enabled = false
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-sample-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 3
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_test_51N6vVBEUMiWXlUMIC6ofmWps7ct4kaV19RxVwtvtSFYenPLqTuLyNgtiSbiC0f5KZa7uwTAzOg3qw0LulyYrxVfj009UP9aelJ
      EOF
    }
    beta_subscriber_app = {
      deploy_enabled = false
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-beta-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 5
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51P14pyBjKbjD0ScP28R0nO0XgkNl2p4G461C9oz6gCLQ78EapmjWusZz922vZWzCUimg7yfRZFk5ecIX0Wxj23An00eTSj9kB8
      EOF
    }
    inovo_subscriber_app = {
      deploy_enabled = false
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-inovo-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 7
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51P14pyBjKbjD0ScP28R0nO0XgkNl2p4G461C9oz6gCLQ78EapmjWusZz922vZWzCUimg7yfRZFk5ecIX0Wxj23An00eTSj9kB8
      EOF
    }
    richmond_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-richmond-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 8
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51P14pyBjKbjD0ScP28R0nO0XgkNl2p4G461C9oz6gCLQ78EapmjWusZz922vZWzCUimg7yfRZFk5ecIX0Wxj23An00eTSj9kB8
VITE_CUSTOM_CLIENT="richmond"
      EOF
    }
    alpha_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-alpha-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 'alpha-X5523'
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_test_51N6vVBEUMiWXlUMIC6ofmWps7ct4kaV19RxVwtvtSFYenPLqTuLyNgtiSbiC0f5KZa7uwTAzOg3qw0LulyYrxVfj009UP9aelJ
VITE_CUSTOM_CLIENT="alpha"
VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
      EOF
    }
    yayzi_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-yayzi-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 10
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51P4L64HJzm6YrVdeC4znqfg3QGEZc4dkw5b0l0LaMinL9Vw0nQpAus4CX501Ox4DeY2HGvDwNzlCYiIAqTeR1ozz008LTMedad
VITE_GOOGLE_TRACKING_ID = 'G-KW1TYS3JEM'
VITE_CUSTOM_CLIENT= "yayzi"
VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
VITE_DISABLE_PHYSICAL_SIM = "false"
      EOF
    }
    yayzi_beta_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-yayzi-beta-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 6
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_test_51N6vVBEUMiWXlUMIC6ofmWps7ct4kaV19RxVwtvtSFYenPLqTuLyNgtiSbiC0f5KZa7uwTAzOg3qw0LulyYrxVfj009UP9aelJ
VITE_CUSTOM_CLIENT="yayzi"
VITE_DISABLE_SIGNUP = 'true'
      EOF
    }
    itsi_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-itsi-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 11
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51PtRsABECGffM4Da64A4PmDxa6FkVawzQXyF1gD6WCG5oXW9D56rkWXMrAwDJ7RaRP7sA34sjPAABzvMe2AczuqA00UzYjSTWK
VITE_CUSTOM_CLIENT="itsi"
VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
VITE_DISABLE_PHYSICAL_SIM = "false"
VITE_GOOGLE_TRACKING_ID = 'G-L6FJ08PGS6'
      EOF
    }
    cove_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-cove-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 12
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51QT0XFLauigQf0yQmgtZDRngxOSZP5rsDBNUzuvpqePgd6KNLm7eUSjWIlA0YmgzIEJMxXN6scIKBJfdJig1ZMfn00KavryfJD
VITE_CUSTOM_CLIENT="cove"
VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
VITE_DISABLE_PHYSICAL_SIM = "true"
      EOF
    }
    perkfon_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-perkfon-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 'X2-PF'
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51Qk1W4DcHUZXRfDDCx1gyDpC4TNvyYNCGEZy3ZxLqQkMI2FbIJL2E1WWZCTpky0XoTB2KEYESLnxLDQHuskRPqQ600e1FJiU8l
VITE_CUSTOM_CLIENT="perkfon"
VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
VITE_DISABLE_PHYSICAL_SIM = "true"
      EOF
    }
    millwall_subscriber_app = {
      deploy_enabled = true
      path           = "shaka/client_subscriber-app_web"
      bucket         = "prod-millwall-subscriber-app-dashboard-frontend"
      config         = <<EOF
VITE_CLIENT_ID = 's97x8733'
VITE_BASE_URL = 'https://nexus.shaka.tel'
VITE_STRIPE_PK = pk_live_51QrJ1fDzLItI5LN3UrSEfPnHupLGvsXJ69SWWql1XvO3TD6E8QvmMhjPZnWxguMMgbcCkQI5YrKojzkTSQPh26Ov00t6ZbRAdN
VITE_CUSTOM_CLIENT="millwall"
VITE_INTERCOM_KEY = P3xeUIpQBwG-gORxpUuvhn4GFr_ofTnxSVfMYqvB
VITE_USE_CUSTOM_OPENID_AUTH = "true"
VITE_GOOGLE_TRACKING_ID = 'G-8R5N9ZDKZV'
      EOF
    }
  }
}



variable "elastic_beanstalks" {
  description = "Map of beanstalks to deploy."
  type        = map(any)

  default = {
    cdr_db = {
      application_name    = "prod-cdr-db-eb-app"
      environment_name    = "prod-cdr-db-eb-env"
      name                = "CDR DB EB"
      lb_name             = "app/awseb--AWSEB-bfvAsUA98AGc/d9b718773218124c"
      lb_dns              = "prod-cdr-db-eb-env.eba-gqxih3vc.eu-west-2.elasticbeanstalk.com"
      lb_domain           = "awseb--AWSEB-bfvAsUA98AGc-201031718.eu-west-2.elb.amazonaws.com"
      lb_zone_id          = "ZHURV8PSTC4K8"
      domain_name         = "cdr-db"
      treat_missing_data  = "notBreaching" # Because not many requests
      default_root_object = "index.html"
    },
    client_dashboard = {
      application_name    = "prod-client-dashboard-eb-app"
      environment_name    = "prod-client-dashboard-eb-env"
      name                = "Client Dashboard EB"
      lb_name             = "app/awseb--AWSEB-BDruNrNCFqEt/5d48f67403fc1c61"
      lb_dns              = "prod-client-dashboard-eb-env.eba-i3zvxvqx.eu-west-2.elasticbeanstalk.com"
      lb_domain           = "awseb--AWSEB-BDruNrNCFqEt-1366327619.eu-west-2.elb.amazonaws.com"
      lb_zone_id          = "ZHURV8PSTC4K8"
      domain_name         = "prod-client-dashboard-sample"
      treat_missing_data  = "notBreaching" # Because not many requests
      default_root_object = "index.html"
    },
    nexus = {
      application_name    = "prod-nexus-eb-app"
      environment_name    = "prod-nexus-eb-env"
      name                = "Nexus EB"
      domain_name         = "nexus"
      lb_name             = "app/awseb--AWSEB-2tpk8jcTsuzk/da9dc7b92f2a5887"
      lb_domain           = "awseb--AWSEB-2tpk8jcTsuzk-836817487.eu-west-2.elb.amazonaws.com"
      lb_zone_id          = "ZHURV8PSTC4K8"
      lb_dns              = "prod-nexus-eb-env.eba-accc8nv8.eu-west-2.elasticbeanstalk.com"
      treat_missing_data  = "notBreaching" # Because not many requests
      default_root_object = "index.html"
    },
    simpnexus = {
      application_name    = "prod-simpnexus-eb-app"
      environment_name    = "prod-simpnexus-eb-env"
      name                = "Simpnexus EB"
      domain_name         = "simpnexus"
      lb_name             = "app/awseb--AWSEB-MET1Q9LEGt28/c0e4e8fdda7c0583"
      lb_domain           = "awseb--AWSEB-MzXJz8SnLUZn-1432486481.eu-west-2.elb.amazonaws.com"
      lb_zone_id          = "ZHURV8PSTC4K8"
      lb_dns              = "prod-simpnexus-eb-env.eba-mke5xvkx.eu-west-2.elasticbeanstalk.com"
      treat_missing_data  = "notBreaching" # Because not many requests
      default_root_object = "index.html"
    },
    sandbox = {
      application_name    = "prod-sandbox-eb-app"
      environment_name    = "prod-sandbox-eb-env"
      name                = "Sandbox EB"
      domain_name         = "sandbox"
      lb_name             = "app/awseb--AWSEB-Uyf2AKSvtLre/50a9a9f113305e02"
      lb_domain           = "awseb--AWSEB-Uyf2AKSvtLre-874467468.eu-west-2.elb.amazonaws.com"
      lb_zone_id          = "ZHURV8PSTC4K8"
      lb_dns              = "prod-sandbox-eb-env.eba-hstqhgtc.eu-west-2.elasticbeanstalk.com"
      treat_missing_data  = "notBreaching" # Because not many requests
      default_root_object = "index.html"
    },
    simpwebapp = {
      application_name        = "prod-simpwebapp-eb-app"
      environment_name        = "prod-simpwebapp-eb-env"
      name                    = "Simpwebapp EB"
      domain_name             = "simpwebapp"
      lb_name                 = "app/awseb--AWSEB-Dpnwm6ozz9SQ/f56a8bfc967abf81"
      lb_domain               = "awseb--AWSEB-Dpnwm6ozz9SQ-1602228648.eu-west-2.elb.amazonaws.com"
      lb_zone_id              = "ZHURV8PSTC4K8"
      lb_dns                  = "prod-simpwebapp-eb-env.eba-b6wbpfnz.eu-west-2.elasticbeanstalk.com"
      treat_missing_data      = "notBreaching" # Because not many requests
      default_root_object     = null
      cloudfront_cert_arn     = "arn:aws:acm:us-east-1:727907215122:certificate/0f094164-0e41-4813-8904-b9100333d44f"
      cloudfront_domain_alias = "simpmobile.com"
    }
  }
}


data "aws_iam_policy_document" "prod_meta_lambda_logging" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
}

resource "aws_iam_policy" "prod_meta_lambda_sqs_read" {
  name        = "prod_meta_lambda_sqs_read"
  path        = "/"
  description = "IAM policy for reading from a sqs queue for a lambda"
  policy      = data.aws_iam_policy_document.prod_meta_lambda_sqs_read.json
}


data "aws_iam_policy_document" "prod_meta_lambda_sqs_read" {
  statement {
    effect = "Allow"

    actions = [
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes"
    ]

    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_meta_lambda_logging" {
  name        = "prod_meta_lambda_logging"
  path        = "/"
  description = "IAM policy for logging from a lambda for production meta"
  policy      = data.aws_iam_policy_document.prod_meta_lambda_logging.json
}

resource "aws_iam_role" "prod_meta_iam_for_lambda" {
  name = "iam_for_lambda"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "prod_meta_lambda_logs" {
  role       = aws_iam_role.prod_meta_iam_for_lambda.name
  policy_arn = aws_iam_policy.prod_meta_lambda_logging.arn
}


resource "aws_iam_role_policy_attachment" "prod_meta_lambda_sqs_read_attach" {
  role       = aws_iam_role.prod_meta_iam_for_lambda.name
  policy_arn = aws_iam_policy.prod_meta_lambda_sqs_read.arn
}

output "lambda_artifact_bucket" {
  value = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
}

locals {
  test_db_creds = sensitive(yamldecode(data.aws_kms_secrets.prod_test_db_creds.plaintext["test-db-creds"]))
}

data "aws_kms_secrets" "prod_test_db_creds" {
  secret {
    name    = "test-db-creds"
    payload = file("${path.module}/test_db_creds.yml.encrypted")
    key_id  = "e73d0e08-6282-47b4-a049-5648fb584809"
  }
}
