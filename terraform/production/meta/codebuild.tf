resource "aws_codebuild_project" "prod_terraform_codebuild_lint_project" {
  name         = "prod-terraform-codebuild-lint-project"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  install:
    commands:
      - "curl -s https://releases.hashicorp.com/terraform/1.6.4/terraform_1.6.4_linux_amd64.zip -o terraform.zip"
      - "unzip terraform.zip -d /usr/local/bin"
      - "chmod 755 /usr/local/bin/terraform"
  pre_build:
    commands:
      - "cd ${local.tf_dir}"
  build:
    commands:
      - "terraform fmt -check -recursive"
EOF
  }
}


resource "aws_codebuild_project" "prod_terraform_codebuild_plan_project" {
  name         = "prod-terraform-codebuild-plan-project"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

env:
  exported-variables:
    - BuildID
    - BuildTag

phases:
  install:
    commands:
      - "curl -s https://releases.hashicorp.com/terraform/1.6.4/terraform_1.6.4_linux_amd64.zip -o terraform.zip"
      - "unzip terraform.zip -d /usr/local/bin"
      - "chmod 755 /usr/local/bin/terraform"
  pre_build:
    commands:
      - "cd ${local.tf_dir}"
      - "terraform init"
  build:
    commands:
      - "terraform plan --out prod_terraform.tfplan"
      - "export BuildID=`echo $CODEBUILD_BUILD_ID | cut -d: -f1`"
      - "export BuildTag=`echo $CODEBUILD_BUILD_ID | cut -d: -f2`"

artifacts:
  name: terraform_plan
  files:
    - ${local.tf_dir}/prod_terraform.tfplan
    EOF
  }
}


resource "aws_codebuild_project" "prod_terraform_codebuild_apply_project" {
  name         = "prod-terraform-codebuild-apply-project"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

env:
  exported-variables:
    - BuildID
    - BuildTag

phases:
  install:
    commands:
      - "curl -s https://releases.hashicorp.com/terraform/1.6.4/terraform_1.6.4_linux_amd64.zip -o terraform.zip"
      - "unzip terraform.zip -d /usr/local/bin"
      - "chmod 755 /usr/local/bin/terraform"
      - "mv $CODEBUILD_SRC_DIR_terraform_plan/${local.tf_dir}/prod_terraform.tfplan ${local.tf_dir}"
  pre_build:
    commands:
      - "cd ${local.tf_dir}"
      - "terraform init"
  build:
    commands:
      - "terraform apply prod_terraform.tfplan"
      - "export BuildID=`echo $CODEBUILD_BUILD_ID | cut -d: -f1`"
      - "export BuildTag=`echo $CODEBUILD_BUILD_ID | cut -d: -f2`"
    EOF
  }
}


resource "aws_codebuild_project" "prod_codebuild_test_code" {
  name         = "prod-terraform-codebuild-test-code"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  install:
    runtime-versions:
      python: 3.11
      nodejs: 18
    commands:
      - "corepack enable"
      - "yum install -y postgresql-libs"
      - "pip install -r requirements.txt"
      - "pip install psycopg_binary"
      - "for req in `ls ./lambda/*/*/requirements.txt`; do pip install -r $req; done"
      - "for req in `ls ./lambda/*/*/test_requirements.txt`; do pip install -r $req; done"
      - "cd ./lambda/shaka/client_dashboard_frontend"
      - "yarn install"
      - "cd ../../../"
  pre_build:
    commands:
      - "./lint.sh"
      - "export DB_HOST=${aws_db_instance.prod_test_db.address}"
      - "export DB_NAME=test_db"
      - "export DB_USER=${local.test_db_creds["DB_USER"]}"
      - "export DB_PASSWORD=${local.test_db_creds["DB_PASSWORD"]}"
  build:
    commands:
      - "./test.sh"
EOF
  }
}



resource "aws_codebuild_project" "prod_meta_codebuild_build_backends" {
  name         = "prod-meta-codebuild-build-backends"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  build:
    commands:
      - "PLANNED_BUILDS="
%{for lambda_name in local.lambda_names~}
      - "cd $CODEBUILD_SRC_DIR"
      - "cd lambda/${lookup(local.lambda_paths, lambda_name)}"
      - "MD5=`find ./ -type f -exec md5sum {} \\; | sort -k 2 | md5sum | cut -f 1 -d' '`"
      - "mkdir -p package"
      - "if test -f ./requirements.txt; then pip install --platform manylinux2014_x86_64 --only-binary=:all: --implementation cp --python-version 3.11 --target ./package -r ./requirements.txt; fi"
      - "cd package"
      - "zip -r ../${lambda_name}.zip . -i . -i \\*"
      - "cd .."
      - "rm -r package"
      - "zip -r ${lambda_name}.zip ."
      - "OBJMD5=$(aws s3api head-object --bucket ${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket} --key lambda-builds/prod/${lambda_name}_zipped.zip | python -c 'import json; import sys;print(json.load(sys.stdin).get(\"Metadata\", {}).get(\"lmd5\"))' || echo '')"
      - "echo $MD5"
      - "echo $OBJMD5"
      - "zip ${lambda_name}_zipped.zip ./${lambda_name}.zip"
      - "if [[ $OBJMD5 != $MD5 ]]; then aws s3 cp ${lambda_name}_zipped.zip s3://${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket}/lambda-builds/prod/${lambda_name}_zipped.zip --metadata lmd5=$MD5; fi"
      - "if [[ $OBJMD5 != $MD5 && '${lookup(aws_codepipeline.prod_lambda_deploy_pipeline, lambda_name, { name = "" }).name}' ]]; then PLANNED_BUILDS=\"$PLANNED_BUILDS ${lambda_name},\"; fi"
      - "if [[ $OBJMD5 != $MD5 && '${lookup(aws_codepipeline.prod_lambda_deploy_pipeline, lambda_name, { name = "" }).name}' ]]; then aws codepipeline start-pipeline-execution --name ${lookup(aws_codepipeline.prod_lambda_deploy_pipeline, lambda_name, { name = "" }).name}; fi"
      - "rm ${lambda_name}_zipped.zip ./${lambda_name}.zip"
%{endfor~}
      - "if [ ! -z \"$PLANNED_BUILDS\" ]; then aws sqs send-message --queue-url ${aws_sqs_queue.prod_pipeline_notification_queue.url} --message-body \"{\\\"planned_builds\\\": \\\"$PLANNED_BUILDS\\\"}\"; fi"
    EOF
  }
}


resource "aws_codebuild_project" "prod_meta_codebuild_build_backends2" {
  name         = "prod-meta-codebuild-build-backends2"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  build:
    commands:
      - "PLANNED_BUILDS="
%{for eb_name in local.eb_names~}
      - "cd $CODEBUILD_SRC_DIR"
      - "cd lambda/${lookup(local.eb_paths, eb_name)}"
      - "MD5=`find ./ -type f -exec md5sum {} \\; | sort -k 2 | md5sum | cut -f 1 -d' '`"
      - "zip -r ${eb_name}.zip ."
      - "OBJMD5=$((aws s3api head-object --bucket ${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket} --key eb-builds/prod/${eb_name}.zip || echo {}) | python -c 'import json; import sys;print(json.load(sys.stdin).get(\"Metadata\", {}).get(\"lmd5\"))' || echo '')"
      - "echo $MD5"
      - "echo $OBJMD5"
      - "if [[ $OBJMD5 != $MD5 ]]; then aws s3 cp ${eb_name}.zip s3://${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket}/eb-builds/prod/${eb_name}.zip --metadata lmd5=$MD5; fi"
      - "if [[ $OBJMD5 != $MD5 && '${lookup(aws_codepipeline.prod_eb_deploy_pipeline, eb_name, { name = "" }).name}' ]]; then PLANNED_BUILDS=\"$PLANNED_BUILDS ${eb_name},\"; fi"
      - "if [[ $OBJMD5 != $MD5 && '${lookup(aws_codepipeline.prod_eb_deploy_pipeline, eb_name, { name = "" }).name}' ]]; then aws codepipeline start-pipeline-execution --name ${lookup(aws_codepipeline.prod_eb_deploy_pipeline, eb_name, { name = "" }).name}; fi"
      - "rm ${eb_name}.zip"
%{endfor~}
      - "if [ ! -z \"$PLANNED_BUILDS\" ]; then aws sqs send-message --queue-url ${aws_sqs_queue.prod_pipeline_notification_queue.url} --message-body \"{\\\"planned_builds\\\": \\\"$PLANNED_BUILDS\\\"}\"; fi"
    EOF
  }
}


resource "aws_codebuild_project" "prod_meta_codebuild_build_frontends" {
  name         = "prod-meta-codebuild-build-frontends"
  service_role = aws_iam_role.prod_terraform_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  build:
    commands:
      - "PLANNED_BUILDS="
%{for frontend_name, frontend_details in var.frontends~}
%{if frontend_details.deploy_enabled == true~}
      - "cd $CODEBUILD_SRC_DIR"
      - "cd lambda/${frontend_details.path}"
      - "echo \"${base64encode(frontend_details.config)}\" | base64 -d > .env.production.local"
      - "MD5=`find ./ -type f -exec md5sum {} \\; | sort -k 2 | md5sum | cut -f 1 -d' '`"
      - "corepack enable"
      - "yarn install"
      - "yarn build"
      - "cd dist"
      - "zip -r ../${frontend_name}.zip ."
      - "cd .."
      - "OBJMD5=$((aws s3api head-object --bucket ${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket} --key frontend-builds/prod/${frontend_name}.zip || echo {}) | python -c 'import json; import sys;print(json.load(sys.stdin).get(\"Metadata\", {}).get(\"lmd5\"))' || echo '')"
      - "if [[ $OBJMD5 != $MD5 ]]; then aws s3 cp ${frontend_name}.zip s3://${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket}/frontend-builds/prod/${frontend_name}.zip --metadata lmd5=$MD5; fi"
      - "if [[ $OBJMD5 != $MD5 && '${lookup(aws_codepipeline.prod_frontend_deploy_pipeline, frontend_name, { name = "" }).name}' ]]; then PLANNED_BUILDS=\"$PLANNED_BUILDS ${frontend_name},\"; fi"
      - "if [[ $OBJMD5 != $MD5 && '${lookup(aws_codepipeline.prod_frontend_deploy_pipeline, frontend_name, { name = "" }).name}' ]]; then aws codepipeline start-pipeline-execution --name ${lookup(aws_codepipeline.prod_frontend_deploy_pipeline, frontend_name, { name = "" }).name}; fi"
      - "rm ${frontend_name}.zip"
%{endif~}
%{endfor~}
      - "if [ ! -z \"$PLANNED_BUILDS\" ]; then aws sqs send-message --queue-url ${aws_sqs_queue.prod_pipeline_notification_queue.url} --message-body \"{\\\"planned_builds\\\": \\\"$PLANNED_BUILDS\\\"}\"; fi"
    EOF
  }
}
