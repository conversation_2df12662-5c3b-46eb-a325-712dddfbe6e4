resource "aws_codepipeline" "prod_terraform_pipeline" {
  name     = "prod-terraform-pipeline"
  role_arn = aws_iam_role.prod_terraform_pipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeStarSourceConnection"
      version          = "1"
      output_artifacts = ["source_output"]
      namespace        = "Source"

      configuration = {
        ConnectionArn    = "arn:aws:codestar-connections:eu-west-2:727907215122:connection/0c74148c-a59e-4c7b-8341-1899d804bb32"
        FullRepositoryId = "shakatel/shaka-deploy"
        BranchName       = "prod-release"
      }
    }
  }

  stage {
    name = "TerraformLint"
    action {
      name            = "TerraformLint"
      category        = "Test"
      owner           = "AWS"
      provider        = "CodeBuild"
      version         = "1"
      input_artifacts = ["source_output"]

      configuration = {
        ProjectName = aws_codebuild_project.prod_terraform_codebuild_lint_project.name

      }
    }
  }

  stage {
    name = "TerraformPlan"
    action {
      name             = "TerraformPlan"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      input_artifacts  = ["source_output"]
      output_artifacts = ["terraform_plan"]
      version          = "1"

      configuration = {
        ProjectName          = aws_codebuild_project.prod_terraform_codebuild_plan_project.name
        EnvironmentVariables = <<EOF
        [
            {"name": "EXECUTION_ID",    "value": "#{codepipeline.PipelineExecutionId}"},
            {"name": "BRANCH",          "value": "#{Source.BranchName}"},
            {"name": "REPO",            "value": "#{Source.FullRepositoryName}"},
            {"name": "COMMIT_ID",       "value": "#{Source.CommitId}"}
        ]
        EOF
      }
    }
  }

  stage {
    name = "TerraformApply"
    action {
      name            = "TerraformApply"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output", "terraform_plan"]
      version         = "1"

      configuration = {
        ProjectName          = aws_codebuild_project.prod_terraform_codebuild_apply_project.name
        PrimarySource        = "source_output"
        EnvironmentVariables = <<EOF
        [
            {"name": "EXECUTION_ID",    "value": "#{codepipeline.PipelineExecutionId}"},
            {"name": "BRANCH",          "value": "#{Source.BranchName}"},
            {"name": "REPO",            "value": "#{Source.FullRepositoryName}"},
            {"name": "COMMIT_ID",       "value": "#{Source.CommitId}"}
        ]
        EOF
      }
    }
  }

  stage {
    name = "TestCode"
    action {
      name            = "TestCode"
      category        = "Test"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      version         = "1"
      configuration = {
        ProjectName = aws_codebuild_project.prod_codebuild_test_code.name
      }
    }
  }

  stage {
    name = "BuildBackends"
    action {
      name            = "BuildBackends"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      version         = "1"
      configuration = {
        ProjectName = aws_codebuild_project.prod_meta_codebuild_build_backends.name
      }
    }
  }

  stage {
    name = "BuildBackends2"
    action {
      name            = "BuildBackends2"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      version         = "1"
      configuration = {
        ProjectName = aws_codebuild_project.prod_meta_codebuild_build_backends2.name
      }
    }
  }

  stage {
    name = "BuildFrontends"
    action {
      name            = "BuildFrontends"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      input_artifacts = ["source_output"]
      version         = "1"
      configuration = {
        ProjectName = aws_codebuild_project.prod_meta_codebuild_build_frontends.name
      }
    }
  }
}



resource "aws_s3_bucket" "prod_terraform_pipeline_artifact_store" {
  bucket = "prod-terraform-pipeline-artifact-store"
}

resource "aws_s3_bucket_versioning" "prod_terraform_pipeline_artifact_store_versioning" {
  bucket = aws_s3_bucket.prod_terraform_pipeline_artifact_store.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_iam_policy" "prod_terraform_pipeline_policy" {
  name        = "prod-terraform-pipeline-policy"
  description = "Policy to allow codepipeline to execute"
  policy      = <<EOF
{
    "Statement": [
        {
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "*",
            "Effect": "Allow",
            "Condition": {
                "StringEqualsIfExists": {
                    "iam:PassedToService": [
                        "cloudformation.amazonaws.com",
                        "elasticbeanstalk.amazonaws.com",
                        "ec2.amazonaws.com",
                        "ecs-tasks.amazonaws.com"
                    ]
                }
            }
        },
        {
            "Action": [
                "codecommit:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "kms:Decrypt"
            ],
            "Resource": "arn:aws:kms:eu-west-2:727907215122:key/e73d0e08-6282-47b4-a049-5648fb584809",
            "Effect": "Allow"
        },
        {
            "Action": [
              "logs:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codedeploy:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codestar-connections:UseConnection"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "elasticbeanstalk:*",
                "ec2:*",
                "elasticloadbalancing:*",
                "autoscaling:*",
                "cloudwatch:*",
                "s3:*",
                "sns:*",
                "cloudformation:*",
                "cloudfront:*",
                "rds:*",
                "sqs:*",
                "ecs:*",
                "events:*",
                "scheduler:*",
                "wafv2:*",
                "acm:*",
                "route53:*",
                "states:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "lambda:InvokeFunction",
                "lambda:ListFunctions"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "opsworks:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "cloudformation:CreateStack",
                "cloudformation:DeleteStack",
                "cloudformation:DescribeStacks",
                "cloudformation:UpdateStack",
                "cloudformation:CreateChangeSet",
                "cloudformation:DeleteChangeSet",
                "cloudformation:DescribeChangeSet",
                "cloudformation:ExecuteChangeSet",
                "cloudformation:SetStackPolicy",
                "cloudformation:ValidateTemplate"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codebuild:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codepipeline:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Effect": "Allow",
            "Action": [
                "devicefarm:ListProjects",
                "devicefarm:ListDevicePools",
                "devicefarm:GetRun",
                "devicefarm:GetUpload",
                "devicefarm:CreateUpload",
                "devicefarm:ScheduleRun"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "servicecatalog:*"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "cloudformation:ValidateTemplate"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ecr:DescribeImages"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "states:DescribeExecution",
                "states:DescribeStateMachine",
                "states:StartExecution"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": "s3:ListBucket",
            "Resource": "arn:aws:s3:::shaka-terraform-state-container"
        },
        {
            "Effect": "Allow",
            "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
            "Resource": "arn:aws:s3:::shaka-terraform-state-container/infra-production"
        },
        {
            "Effect": "Allow",
            "Action": [
                "dynamodb:DescribeTable",
                "dynamodb:GetItem",
                "dynamodb:PutItem",
                "dynamodb:DeleteItem"
            ],
            "Resource": "arn:aws:dynamodb:*:*:table/shaka-terraform-state-locking"
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:*"
            ],
            "Resource": "arn:aws:iam::727907215122:*"
        },
        {
            "Effect": "Allow",
            "Action": [
              "codestar-notifications:*",
              "codestar-connections:*"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
              "lambda:*"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "appconfig:StartDeployment",
                "appconfig:StopDeployment",
                "appconfig:GetDeployment"
            ],
            "Resource": "*"
        }
    ],
    "Version": "2012-10-17"
}
EOF
}

resource "aws_iam_role" "prod_terraform_pipeline_role" {
  name               = "prod-terraform-pipeline-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "codepipeline.amazonaws.com"
      },
      "Effect": "Allow"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "prod_terraform_pipeline_attach" {
  role       = aws_iam_role.prod_terraform_pipeline_role.name
  policy_arn = aws_iam_policy.prod_terraform_pipeline_policy.arn
}
