resource "aws_elastic_beanstalk_application" "prod_client_dashboard_eb_app" {
  name = "prod-client-dashboard-eb-app"
}

resource "aws_cloudwatch_log_group" "prod_client_dashboard_eb_log_group" {
  name              = "/aws/elasticbeanstalk/prod-client-dashboard-eb-env/django"
  retention_in_days = 7
}

resource "aws_elastic_beanstalk_environment" "prod_client_dashboard_eb_env" {
  name                = "prod-client-dashboard-eb-env"
  application         = aws_elastic_beanstalk_application.prod_client_dashboard_eb_app.name
  solution_stack_name = "64bit Amazon Linux 2023 v4.0.6 running Python 3.11"

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DJANGO_SETTINGS_MODULE"
    value     = "client_dashboard.production_settings"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_USER"
    value     = local.client_dashboard_creds["DB_USER"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_PASSWORD"
    value     = local.client_dashboard_creds["DB_PASSWORD"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_HOST"
    value     = aws_db_instance.prod_client_dashboard.address
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_NAME"
    value     = aws_db_instance.prod_client_dashboard.db_name
    resource  = ""
  }


  setting {
    namespace = "aws:ec2:instances"
    name      = "InstanceTypes"
    value     = "t2.micro"
    resource  = ""
  }
  setting {
    namespace = "aws:elasticbeanstalk:cloudwatch:logs"
    name      = "StreamLogs"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = 1
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.prod_eb_profile.name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:container:python"
    name      = "WSGIPath"
    value     = "client_dashboard.wsgi:application"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "LoadBalancerType"
    value     = "application"
    resource  = ""
  }


  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "ListenerEnabled"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "Protocol"
    value     = "HTTPS"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = "arn:aws:acm:eu-west-2:************:certificate/97cc242c-95d7-40a5-a6b0-4b4fb0d6ecc2"
    resource  = ""
  }
}


# https://epam.github.io/edp-install/operator-guide/waf-tf-configuration/
resource "aws_wafv2_regex_pattern_set" "prod_client_dashboard_host_regex" {
  name  = "prod-client-dashboard-host-regex"
  scope = "REGIONAL"

  regular_expression {
    regex_string = "prod-client-dashboard-sample.shaka.tel"
  }
}

resource "aws_wafv2_web_acl" "prod_client_dashboard_host_acl" {
  name  = "prod-client-dashboard-host-acl"
  scope = "REGIONAL"

  default_action {
    block {}
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 2

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    override_action {
      none {}
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 3

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "PreventHostInjections"
    priority = 0

    statement {
      regex_pattern_set_reference_statement {
        arn = aws_wafv2_regex_pattern_set.prod_client_dashboard_host_regex.arn

        field_to_match {
          single_header {
            name = "host"
          }
        }

        text_transformation {
          priority = 0
          type     = "NONE"
        }
      }
    }

    action {
      allow {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "PreventHostInjections"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "prod-client-dashboard-host-acl"
    sampled_requests_enabled   = true
  }
}

resource "aws_wafv2_web_acl_association" "prod_waf_client_dashboard_alb" {
  resource_arn = aws_elastic_beanstalk_environment.prod_client_dashboard_eb_env.load_balancers[0]
  web_acl_arn  = aws_wafv2_web_acl.prod_client_dashboard_host_acl.arn
}



resource "aws_db_instance" "prod_client_dashboard" {
  allocated_storage   = 10
  db_name             = "client_dashboard"
  engine              = "postgres"
  engine_version      = "15.8"
  instance_class      = "db.t3.small"
  username            = local.client_dashboard_creds["DB_USER"]
  password            = local.client_dashboard_creds["DB_PASSWORD"]
  identifier          = "prod-client-dashboard"
  deletion_protection = true
  lifecycle {
    prevent_destroy = true
  }
  backup_retention_period = 7
}
