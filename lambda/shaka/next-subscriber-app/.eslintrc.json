{"extends": "next/core-web-vitals", "plugins": ["import", "@typescript-eslint"], "rules": {"react/no-unescaped-entities": ["error", {"forbid": [{"char": "'", "alternatives": ["&apos;"]}, {"char": "\"", "alternatives": ["&quot;"]}, {"char": ">", "alternatives": ["&gt;"]}, {"char": "}", "alternatives": ["&#125;"]}]}], "import/no-default-export": "error", "react/function-component-definition": ["error", {"namedComponents": "function-declaration", "unnamedComponents": "function-expression"}], "@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports"}]}, "settings": {"next": {"rootDir": "./src"}}}