import React, { useState } from 'react';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { PlainCard } from '@/components/plain-card/plain-card';
import { useMediaQuery } from '@/hooks/useMediaQuery';

const dummyItems = [
  {
    id: 2,
    name: '3GB monthly global roaming',
    amount: 3,
    quantity: 1
  },
  {
    id: 3,
    name: 'Unlimited SIM plan',
    amount: 2,
    quantity: 1
  },
  {
    id: 4,
    name: '40GB SIM plan',
    amount: 4,
    quantity: 2
  }
];

// TODO: useMediaQuery hook to render conditionally
// same pattern in layout instead of duplication
export function OrderSummary() {
  const isMobile = useMediaQuery(1024);
  const totalCost = dummyItems.reduce(
    (acc, item) => acc + item.amount * item.quantity,
    0
  );

  console.log(isMobile, 'isMobile');

  return isMobile ? (
    <OrderSummaryMobile amount={totalCost} />
  ) : (
    <OrderSummaryDesktop amount={totalCost} />
  );
}

interface OrderSummaryProps {
  amount: number;
}
function OrderSummaryDesktop({ amount }: OrderSummaryProps) {
  return (
    <PlainCard className="bg-secondary top-8 order-1 block self-start px-4 py-7 lg:sticky lg:order-2">
      <h3>Order summary</h3>
      <Divider />
      <ol className="space-y-4">
        {dummyItems.map((item) => (
          <OrderSummaryRow
            key={item.id}
            planName={item.name}
            amount={item.amount}
            quantity={item.quantity}
          />
        ))}
      </ol>
      <Divider />
      <TotalCost amount={amount} />
    </PlainCard>
  );
}

function OrderSummaryMobile({ amount }: OrderSummaryProps) {
  const [toggleSummary, setToggleSummary] = useState(false);

  return (
    <div className="bg-blueberry-subtle p-4">
      <button
        aria-expanded={toggleSummary}
        aria-controls="order-summary-panel"
        onClick={() => setToggleSummary(!toggleSummary)}
        className="text-xxxs flex w-full items-center justify-between leading-6 font-bold"
      >
        Order summary
        {!toggleSummary ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M12 16.8105L3.96966 8.78022L5.03032 7.71956L12 14.6892L18.9697 7.71956L20.0303 8.78022L12 16.8105Z"
              fill="#141414"
            />
          </svg>
        ) : (
          <>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12 7.18945L20.0303 15.2198L18.9697 16.2804L12 9.31077L5.03033 16.2804L3.96967 15.2198L12 7.18945Z"
                fill="#141414"
              />
            </svg>
          </>
        )}
      </button>
      {toggleSummary && (
        <div aria-hidden={!toggleSummary} hidden={!toggleSummary}>
          <ol className="mt-6 space-y-4">
            {dummyItems.map((item) => (
              <OrderSummaryRowMobile
                key={item.id}
                planName={item.name}
                amount={item.amount}
                quantity={item.quantity}
              />
            ))}
          </ol>
          <Divider className="mb-6" />
          <TotalCost amount={amount} />
        </div>
      )}
    </div>
  );
}

interface OrderSummaryRowProps {
  planName: string;
  amount: number;
  quantity: number;
}

function OrderSummaryRow({ planName, amount, quantity }: OrderSummaryRowProps) {
  return (
    <li className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <span className="bg-gray-subtle text-xxxs rounded-xs px-[5px] py-[2px] font-semibold">
          {quantity}x
        </span>
        <p className="text-xxxs">{planName}</p>
      </div>
      <strong className="text-default">£{quantity * amount}</strong>
    </li>
  );
}

function OrderSummaryRowMobile({
  planName,
  amount,
  quantity
}: OrderSummaryRowProps) {
  return (
    <li className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <span className="bg-secondary text-xxxs rounded-xs px-[5px] py-[2px] font-semibold">
          {quantity}x
        </span>
        <p className="text-xxxs">{planName}</p>
      </div>
      <strong className="text-default">£{quantity * amount}</strong>
    </li>
  );
}

// extarct  - make more generic ? - used in multiple places
interface TotalCostProps {
  amount: number;
}

export function TotalCost({ amount }: TotalCostProps) {
  return (
    <section
      aria-label="Order total"
      className="flex items-end justify-between"
    >
      <dl className="flex w-full flex-col items-end">
        <div className="flex w-full items-end justify-between">
          <dt className="text-default">Total cost</dt>
          <dd className="text-default font-bold">
            <strong>£{amount} monthly</strong>
          </dd>
        </div>
        <span className="text-xxxs text-right">(including VAT)</span>
      </dl>
    </section>
  );
}
