import {
  DataPlanCard,
  DataPlanCardProps
} from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plan-card';
import { DataPlanCardWithFooter } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plan-with-footer';
import React from 'react';
import { QuantityButtons } from '@/src/uswitch/app/signup/_components/data-plan-cards/quantity-buttons';
import { FullDetailsButton } from '@/src/uswitch/app/signup/_components/full-details-button/full-details-button';
import { basicPlanCardData } from '@/src/uswitch/app/signup/plan-selection/page';

interface PlanCardProps extends React.PropsWithChildren {
  quantity: number;
  planDetails: typeof basicPlanCardData;
  handleIncrement: () => void;
  handleDecrement: () => void;
}

// TODO: better aprroach for dataDetails
export function PlanCard({
  quantity,
  planDetails,
  handleIncrement,
  handleDecrement,
  children
}: PlanCardProps) {
  return (
    <div className="grid grid-cols-2 gap-y-3 px-2 pb-4 lg:grid-cols-[1fr_1fr_auto] lg:grid-rows-[1fr_auto] lg:gap-x-2 lg:gap-y-4 lg:px-3">
      <DataPlanCard planDetails={planDetails} />
      <DataPlanCardWithFooter planDetails={planDetails} />
      <div className="order-4 col-span-3 flex flex-col items-center gap-6 self-start lg:order-3 lg:col-start-3 lg:row-start-1">
        <FullDetailsButton />
        <QuantityButtons
          quantity={quantity}
          handleIncrement={handleIncrement}
          handleDecrement={handleDecrement}
        />
      </div>
      {children}
    </div>
  );
}
