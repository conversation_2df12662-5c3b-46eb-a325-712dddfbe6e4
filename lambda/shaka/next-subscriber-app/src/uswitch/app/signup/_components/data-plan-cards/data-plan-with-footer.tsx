import React from 'react';
import { basicPlanCardData } from '@/src/uswitch/app/signup/plan-selection/page';

interface DataPlanCardWithFooterProps {
  planDetails: typeof basicPlanCardData;
}

export function DataPlanCardWithFooter({
  planDetails
}: DataPlanCardWithFooterProps) {
  const { plan } = planDetails;
  return (
    <div className="bg-blueberry-subtle border-blueberry-light order-2 ml-1 rounded-sm border text-center lg:col-start-2 lg:row-start-1">
      <div className="flex flex-wrap items-end justify-center gap-x-1 p-3">
        <h2 className="lg:text-sm">{plan.title}</h2>
        <p className="text-xxxs lg:text-default lg:leading-7">
          {plan.description}
        </p>
      </div>
      <hr className="border-blueberry-light" />
      <div className="p-1">
        <p className="text-xxxs font-bold">{plan.footerText}</p>
      </div>
    </div>
  );
}
