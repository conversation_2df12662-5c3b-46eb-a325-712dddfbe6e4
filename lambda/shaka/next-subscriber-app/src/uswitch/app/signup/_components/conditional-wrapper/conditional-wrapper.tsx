import React from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface ResponsiveCardProps extends React.PropsWithChildren {
  className?: string;
}

export function ResponsiveCard({ children, className }: ResponsiveCardProps) {
  const isMobile = useMediaQuery(1024);
  if (isMobile) {
    return <section className={className}>{children}</section>;
  }
  return <PlainCard className={className}>{children}</PlainCard>;
}
