import React from 'react';
import { basicPlanCardData } from '@/src/uswitch/app/signup/plan-selection/page';

export interface DataPlanCardProps {
  planDetails: typeof basicPlanCardData;
}

export function DataPlanCard({ planDetails }: DataPlanCardProps) {
  const { plan } = planDetails;
  return (
    <div className="bg-blueberry-subtle border-blueberry-light order-1 mr-1 w-full rounded-sm border p-3 text-center lg:col-start-1 lg:row-start-1">
      <div className="flex flex-wrap items-end justify-center gap-x-1">
        <h2 className="lg:text-sm">{plan.title}</h2>
        <p className="text-xxxs lg:text-default lg:leading-7">
          {plan.description}
        </p>
      </div>
      <p className="text-xxxs lg:text-default mt-2 leading-none lg:mt-0 lg:leading-7">
        {plan.footerText}
      </p>
    </div>
  );
}
