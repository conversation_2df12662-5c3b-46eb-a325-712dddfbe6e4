import React from 'react';
import { MinusIcon, PlusIcon } from '@/icons/icons';

// TODO: focus visible on click !!
interface QuantityButtonsProps {
  quantity: number;
  handleIncrement: () => void;
  handleDecrement: () => void;
}
export function QuantityButtons({
  quantity,
  handleIncrement,
  handleDecrement
}: QuantityButtonsProps) {
  return (
    <div className="flex items-center gap-4 lg:order-1">
      <button
        aria-disabled={quantity <= 1}
        onClick={handleDecrement}
        className="bg-primary text-secondary flex h-8 w-8 cursor-pointer items-center justify-center rounded-full outline disabled:cursor-not-allowed disabled:opacity-20"
        disabled={quantity <= 1}
      >
        <PlusIcon />
      </button>
      <output className="border-border flex h-8 w-8 items-center justify-center rounded border font-semibold">
        {quantity}
      </output>
      <button
        aria-disabled={quantity >= 5}
        onClick={handleIncrement}
        className="bg-primary text-secondary flex h-8 w-8 cursor-pointer items-center justify-center rounded-full outline disabled:cursor-not-allowed disabled:opacity-20"
        disabled={quantity >= 5}
      >
        <MinusIcon />
      </button>
    </div>
  );
}
