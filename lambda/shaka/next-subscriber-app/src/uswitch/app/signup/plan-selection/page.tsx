'use client';

import React, { useState } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import { OrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { usePathname } from 'next/navigation';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { NetworkInfo } from '@/src/uswitch/app/signup/_components/netword-info/network-info';
import { PlanCard } from '@/src/uswitch/app/signup/_components/data-plan-cards/plan-card';
import { DataPlanDetails } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plans-details';
import CtaButton from '@/src/uswitch/app/signup/_components/cta-button/cta-button';
import {
  ConditionalWrapper,
  DesktopOnly
} from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { DataPlanCard } from '../_components/data-plan-cards/data-plan-card';
import { DataPlanCardWithFooter } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plan-with-footer';
import {
  basicPlanCardData,
  dummyGlobalRoamingCardsData,
  familyPlanCardData
} from '@/src/uswitch/constants/constants';

// TODO: heading hierarchy !
export default function PlanSelection() {
  const pathname = usePathname();
  const showProgressBar =
    pathname !== ROUTES_CONFIG['successful-payment'].path &&
    pathname !== ROUTES_CONFIG['payment-error'].path;

  const [basicPlanQuantity, setBasicPlanQuantity] = useState(1);
  const [bigFamilyPlanQuantity, setBigFamilyPlanQuantity] = useState(0);
  const [selectedCardId, setSelectedCardId] = useState<number>(1);

  const handleBigFamilyPlanIncrement = () => {
    if (bigFamilyPlanQuantity >= 5) return;
    setBigFamilyPlanQuantity((prev) => prev + 1);
  };

  const handleBigFamilyPlanDecrement = () => {
    if (bigFamilyPlanQuantity > 0) {
      setBigFamilyPlanQuantity((prev) => prev - 1);
    }
  };
  const handleBasicPlanIncrement = () => {
    setBasicPlanQuantity((prev) => prev + 1);
  };

  const handleBasicPlanDecrement = () => {
    if (basicPlanQuantity > 1) {
      setBasicPlanQuantity((prev) => prev - 1);
    }
  };

  return (
    <>
      <Wrapper>
        <div className="order-2 flex flex-col gap-4 lg:order-1">
          <ConditionalWrapper className="p-6">
            {showProgressBar && (
              <DesktopOnly>
                <ProgressBar withLabel ariaLabel="signup progress bar">
                  <ProgressBar.Track>
                    <ProgressBar.Bar className="bg-blueberry mb-6" />
                  </ProgressBar.Track>
                </ProgressBar>
              </DesktopOnly>
            )}
            <h1 className="mb-4 lg:mb-0">Plan selection</h1>
            <Divider className="my-6 hidden lg:block" />
            <PlainCard className="p-0">
              <NetworkInfo />
              <PlanCard
                plans={
                  <>
                    <DataPlanCard planDetails={basicPlanCardData.plan} />
                    <DataPlanCardWithFooter
                      planDetails={basicPlanCardData.pricing}
                    />
                  </>
                }
                quantity={basicPlanQuantity}
                handleIncrement={handleBasicPlanIncrement}
                handleDecrement={handleBasicPlanDecrement}
              >
                <DataPlanDetails>
                  <h3 className="mb-2 text-[18px] lg:mb-0">20GB in Europe</h3>
                  <div className="border-border hidden border opacity-20 lg:block" />
                  <h3 className="text-[18px]">
                    Unlimited UK calls, texts and data
                  </h3>
                </DataPlanDetails>
              </PlanCard>
            </PlainCard>
            <h2 className="mt-8 mb-2">Global roaming</h2>
            <p className="mb-6">Add monthly travel data now to save 30%</p>
            <div className="grid grid-cols-2 gap-2 lg:w-fit lg:grid-cols-[auto_auto_auto_auto]">
              {dummyGlobalRoamingCardsData.map((cardData) => (
                <RoamingCard
                  key={cardData.id}
                  selectedCardId={selectedCardId}
                  cardData={cardData}
                  setSelectedCardId={setSelectedCardId}
                />
              ))}
            </div>
            <Divider className="mb-6" />
            <h2 className="my-2">Big family ?</h2>
            <p className="mb-6">
              Add up to 5 special rate plans for your family members
            </p>
            <PlainCard className="p-0">
              <NetworkInfo />
              <PlanCard
                plans={
                  <>
                    <DataPlanCard planDetails={familyPlanCardData.plan} />
                    <DataPlanCardWithFooter
                      planDetails={familyPlanCardData.pricing}
                    />
                  </>
                }
                quantity={bigFamilyPlanQuantity}
                handleIncrement={handleBigFamilyPlanIncrement}
                handleDecrement={handleBigFamilyPlanDecrement}
              >
                <DataPlanDetails>
                  <h3 className="mb-2 text-[18px] lg:mb-0">10GB in Europe</h3>
                  <div className="border-border hidden border opacity-20 lg:block" />
                  <h3 className="text-[18px]">
                    Unlimited UK calls, texts and data
                  </h3>
                </DataPlanDetails>
              </PlanCard>
            </PlainCard>
            <Divider />
            <TotalCost amount={12} />
            <CtaButton
              className="mt-6 hidden lg:inline-block"
              text="Go to billing"
              href={ROUTES_CONFIG['number-porting'].path}
            />
          </ConditionalWrapper>
        </div>
        {/*TODO: pass all calc as prop or use url !!*/}
        <OrderSummary />
      </Wrapper>
      <div className="px-6">
        <CtaButton
          className="inline-block lg:hidden"
          text="Go to billing"
          href={ROUTES_CONFIG['number-porting'].path}
        />
      </div>
    </>
  );
}

interface RoamingCardGridProps {
  selectedCardId: number;
  cardData: (typeof dummyGlobalRoamingCardsData)[number];
  setSelectedCardId: (id: number) => void;
}
function RoamingCard({
  selectedCardId,
  cardData,
  setSelectedCardId
}: RoamingCardGridProps) {
  const isActive = selectedCardId === cardData.id;
  return (
    <button
      key={cardData.id}
      type="button"
      onClick={() => setSelectedCardId(cardData.id)}
      className={`border-border cursor-pointer rounded border px-4 py-2 text-center hover:bg-[#f3f3f3] ${isActive ? 'outline-primary outline' : ''}`}
      aria-pressed={isActive}
    >
      <div className="text-[18px] font-bold">{cardData.data}GB data</div>
      <div>£{cardData.price}</div>
    </button>
  );
}

interface TotalCostProps {
  amount: number;
}

function TotalCost({ amount }: TotalCostProps) {
  return (
    <section
      aria-label="Order total"
      className="mt-6 flex items-end justify-between"
    >
      <dl className="flex w-full flex-col items-end">
        <div className="flex w-full items-end justify-between">
          <dt className="text-xs font-bold lg:text-base">Total cost</dt>
          <dd className="text-xs font-bold lg:text-base">
            <strong>£{amount} </strong>{' '}
            <span className="text-default text-xs font-normal lg:text-[18px]">
              a month
            </span>
          </dd>
        </div>
        <span className="text-xxxs text-right">(including VAT)</span>
      </dl>
    </section>
  );
}
