'use client';

import Image from 'next/image';
import { Wizard<PERSON>rovider } from '@/context/wizard/wizard';
import React, { ReactNode } from 'react';
import {
  STEPS,
  STEP_ROUTES,
  type Step
} from '@/src/uswitch/app/signup/_config/steps';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { NavBar } from '@/components/nav-bar/nav-bar';
import logo from 'public/images/UswitchLogo.png';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { usePathname } from 'next/navigation';
import { useMediaQuery } from '@/hooks/useMediaQuery';

export default function SignupLayout({ children }: { children: ReactNode }) {
  const isMobile = useMediaQuery(1024);
  const pathname = usePathname();
  const showProgressBar =
    pathname !== ROUTES_CONFIG['successful-payment'].path &&
    pathname !== ROUTES_CONFIG['payment-error'].path;

  return (
    <WizardProvider<Step> steps={STEPS} stepRoutes={STEP_ROUTES}>
      <NavBar
        logo={<Image src={logo} alt="uswitch logo" width={95} height={24} />}
        className="min-h-[56px]"
      />
      {isMobile ? (
        <main className="block lg:hidden">
          {showProgressBar && (
            <div className="px-[var(--spacing-6)] pt-[var(--spacing-6)]">
              <ProgressBar withLabel ariaLabel="signup progress bar">
                <ProgressBar.Track>
                  <ProgressBar.Bar className="bg-blueberry mb-6" />
                </ProgressBar.Track>
              </ProgressBar>
            </div>
          )}
          {children}
        </main>
      ) : (
        <main className="layout hidden lg:block">{children}</main>
      )}
    </WizardProvider>
  );
}
