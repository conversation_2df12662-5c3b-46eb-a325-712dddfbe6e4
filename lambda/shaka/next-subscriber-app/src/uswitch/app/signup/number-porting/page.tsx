'use client';

import { useWizard } from '@/context/wizard/wizard';
import React from 'react';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import { PlainCard } from '@/components/plain-card/plain-card';

// todo:
// bug : required double esc key press
// initial focus is on entire modal

export default function NumberPorting() {
  const { goToNextStep, goBackToPreviousStep } = useWizard();
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <>
      <Wrapper>
        <div className="order-2 flex flex-col gap-4 lg:order-1">
          <PlainCard>
            <h1>Want to keep an exisiting number?</h1>
          </PlainCard>
        </div>
        <PlainCard className="bg-secondary top-8 order-1 block self-start lg:sticky lg:order-2">
          ASIDE 2
        </PlainCard>
      </Wrapper>
      <button onClick={goToNextStep}>Next</button>
      <button onClick={goBackToPreviousStep}>Back</button>
    </>
  );
}
