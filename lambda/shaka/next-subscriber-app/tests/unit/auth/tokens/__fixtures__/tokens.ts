import { AuthTokens } from '@/auth/tokens/token-service';

export const createTestTokens = (
  overrides: Partial<AuthTokens> = {}
): AuthTokens => ({
  accessToken: 'test-access-token',
  refreshToken: 'test-refresh-token',
  idToken: 'test-id-token',
  expiresAt: Date.now() + 3600 * 1000, // 1 hour from now
  ...overrides
});

export const createExpiredTestTokens = (): AuthTokens => ({
  accessToken: 'expired-access-token',
  refreshToken: 'expired-refresh-token',
  idToken: 'expired-id-token',
  expiresAt: Date.now() - 1000 // 1 second ago
});

export const createMalformedTokens = (): string => 'invalid-json';

export const createLargeTokens = (): AuthTokens => {
  const largeString = 'x'.repeat(5 * 1024 * 1024); // 5MB
  return {
    accessToken: largeString,
    refreshToken: largeString,
    idToken: largeString,
    expiresAt: Date.now() + 3600 * 1000
  };
};
