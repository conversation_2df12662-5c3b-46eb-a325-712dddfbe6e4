type StorageItems = Record<string, string>;

class MockStorage {
  private store: StorageItems = {};
  private listeners: Record<string, Array<(data: any) => void>> = {};

  getItem(key: string): string | null {
    return this.store[key] || null;
  }

  setItem(key: string, value: string): void {
    this.store[key] = value;
    this.emit('storage', { key, newValue: value });
  }

  removeItem(key: string): void {
    delete this.store[key];
    this.emit('storage', { key, newValue: null });
  }

  clear(): void {
    this.store = {};
    this.emit('storage', { key: null, newValue: null });
  }

  key(index: number): string | null {
    return Object.keys(this.store)[index] || null;
  }

  get length(): number {
    return Object.keys(this.store).length;
  }

  // Event emitter methods
  addEventListener(event: string, listener: () => void) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  removeEventListener(event: string, listener: () => void) {
    if (!this.listeners[event]) return;
    const index = this.listeners[event].indexOf(listener);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
    }
  }

  private emit(event: string, data: any) {
    const listeners = this.listeners[event] || [];
    listeners.forEach((listener) => {
      try {
        listener.call(this, data);
      } catch (e) {
        console.error('Error in storage event listener:', e);
      }
    });
  }
}

// Create a singleton instance
const mockStorage = new MockStorage();

// Mock the global localStorage and sessionStorage
Object.defineProperty(window, 'localStorage', {
  value: mockStorage,
  writable: true
});

// Helper to clear storage between tests
export const clearMockStorage = () => {
  mockStorage.clear();
};

// Helper to simulate storage event
export const triggerStorageEvent = (key: string, newValue: string | null) => {
  const oldValue = mockStorage.getItem(key);
  if (newValue === null) {
    mockStorage.removeItem(key);
  } else {
    mockStorage.setItem(key, newValue);
  }
  window.dispatchEvent(
    new StorageEvent('storage', {
      key,
      newValue,
      oldValue,
      storageArea: mockStorage,
      url: window.location.href
    })
  );
};

export default mockStorage;
