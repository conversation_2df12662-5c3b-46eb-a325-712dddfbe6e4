import { useAuth } from '../../auth/hooks/use-auth';
import React, { useState } from 'react';

export const AuthStatus = () => {
  const { isAuthenticated, isLoading, error, login, logout, signup } =
    useAuth();
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [signupEmail, setSignupEmail] = useState('');
  const [signupPassword, setSignupPassword] = useState('');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    await login({ email: loginEmail, password: loginPassword });
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    await signup({ email: signupEmail, password: signupPassword });
  };

  return (
    <div>
      <div data-testid="auth-status">{isAuthenticated ? 'yes' : 'no'}</div>
      <div data-testid="auth-loading">{isLoading ? 'loading' : 'idle'}</div>
      <div data-testid="auth-error">{error || ''}</div>
      <form onSubmit={handleLogin}>
        <input
          aria-label="login-email"
          type="email"
          value={loginEmail}
          onChange={(e) => setLoginEmail(e.target.value)}
          disabled={isLoading}
        />
        <input
          aria-label="login-password"
          type="password"
          value={loginPassword}
          onChange={(e) => setLoginPassword(e.target.value)}
          disabled={isLoading}
        />
        <button type="submit" disabled={isLoading}>
          Login
        </button>
      </form>
      <form onSubmit={handleSignup}>
        <input
          aria-label="signup-email"
          type="email"
          value={signupEmail}
          onChange={(e) => setSignupEmail(e.target.value)}
          disabled={isLoading}
        />
        <input
          aria-label="signup-password"
          type="password"
          value={signupPassword}
          onChange={(e) => setSignupPassword(e.target.value)}
          disabled={isLoading}
        />
        <button type="submit" disabled={isLoading}>
          Signup
        </button>
      </form>
      <button onClick={logout} disabled={isLoading}>
        Logout
      </button>
    </div>
  );
};
