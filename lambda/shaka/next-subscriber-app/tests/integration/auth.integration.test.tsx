import { vi } from 'vitest';
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    pathname: '/'
  }),
  usePathname: () => '/'
}));

import React from 'react';
import { describe, it, expect, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TokenService } from '../../auth/tokens/token-service';
import { AuthProvider } from '../../auth/context/auth-context';
import { customRender } from '../utils/custom-render';
import { AuthStatus } from './auth-test-form';

describe('AuthProvider integration', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('login flow: sets tokens and updates state', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    const emailInput = screen.getByLabelText(/login-email/i);
    const passwordInput = screen.getByLabelText(/login-password/i);

    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123');
    await userEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('yes');
      expect(screen.getByTestId('auth-loading').textContent).toBe('idle');
      expect(screen.getByTestId('auth-error').textContent).toBe('');
    });
  });

  it('logout flow: clears tokens and updates state', async () => {
    const tokenService = new TokenService();

    tokenService.setTokens({
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      idToken: 'mock-id-token',
      expiresAt: Date.now() + 60 * 60 * 1000
    });

    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('yes');
    });

    await userEvent.click(screen.getByText('Logout'));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
    });

    await waitFor(() => {
      expect(tokenService.getAccessToken()).toBeNull();
      expect(tokenService.getRefreshToken()).toBeNull();
    });
  });

  it('login error: shows error on invalid credentials', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    const emailInput = screen.getByLabelText(/login-email/i);
    const passwordInput = screen.getByLabelText(/login-password/i);

    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'wrongpassword');
    await userEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
      expect(screen.getByTestId('auth-loading').textContent).toBe('idle');
      expect(
        (screen.getByTestId('auth-error').textContent ?? '').toLowerCase()
      ).toMatch(/invalid credentials/);
    });
  });

  it('multi-tab sync: logout in one tab logs out in another', async () => {
    // TODO:
    // An update to AuthProvider inside a test was not wrapped in act(...).
    //   When testing, code that causes React state updates should be wrapped into act(...):
    // act(() => {
    //   /* fire events that update state */
    // });
    // /* assert on the output */
    // This ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
    //   [AuthProvider] useEffect runs: state changed { isAuthenticated: false, isLoading: false, error: null }

    // END TODO

    // Step 1: Simulate user is logged in by pre-populating valid tokens in localStorage
    const tokenService = new TokenService();

    tokenService.setTokens({
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      idToken: 'mock-id-token',
      expiresAt: Date.now() + 60 * 60 * 1000 // 1 hour in the future
    });

    // Step 2: Render the AuthProvider and AuthStatus component
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    // Step 3: Assert that the UI shows authenticated state after render
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('yes');
    });

    // Step 4: Simulate logout in another browser tab by clearing tokens and dispatching a storage event
    tokenService.clearTokens();
    window.dispatchEvent(new StorageEvent('storage', { key: 'auth_tokens' }));

    // Step 5: Assert that the UI updates to unauthenticated state
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
    });
  });

  it('signup flow: creates account and authenticates user', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    const emailInput = screen.getByLabelText(/login-email/i);
    const passwordInput = screen.getByLabelText(/login-password/i);

    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'newpassword');
    await userEvent.click(screen.getByRole('button', { name: /signup/i }));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('yes');
      expect(screen.getByTestId('auth-loading').textContent).toBe('idle');
      expect(screen.getByTestId('auth-error').textContent).toBe('');
    });
  });

  it('token refresh: refreshes token and keeps user authenticated', async () => {
    // Pre-populate expired access token, valid refresh token
    const tokenService = new TokenService();

    tokenService.setTokens({
      accessToken: 'expired-access-token',
      refreshToken: 'mock-refresh-token',
      idToken: 'mock-id-token',
      expiresAt: Date.now() - 10000 // Expired
    });
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );
    // Should trigger refresh flow and authenticate user
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('yes');
    });
  });

  it('token refresh: logs out if refresh token is invalid', async () => {
    const tokenService = new TokenService();

    tokenService.setTokens({
      accessToken: 'expired-access-token',
      refreshToken: 'invalid-refresh-token',
      idToken: 'mock-id-token',
      expiresAt: Date.now() - 10000
    });

    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    // Wait for the refresh token operation to complete and tokens to be cleared
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
      expect(tokenService.getAccessToken()).toBeNull();
      expect(tokenService.getRefreshToken()).toBeNull();
    });
  });

  it('signup: shows error when email already exists', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    const emailInput = screen.getByLabelText(/signup-email/i);
    const passwordInput = screen.getByLabelText(/signup-password/i);
    await userEvent.type(emailInput, '<EMAIL>');

    await userEvent.type(passwordInput, 'password123');
    await userEvent.click(screen.getByRole('button', { name: /signup/i }));
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
      expect(
        (screen.getByTestId('auth-error').textContent ?? '').toLowerCase()
      ).toMatch(/already/);
    });
  });

  it('logout: can logout when already logged out (no error)', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );
    // Should be unauthenticated
    expect(screen.getByTestId('auth-status').textContent).toBe('no');
    // Click logout (should be disabled, but test anyway)
    await userEvent.click(screen.getByRole('button', { name: /logout/i }));
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
    });
  });

  it('logout: clears state even with expired/invalid tokens', async () => {
    const tokenService = new TokenService();

    tokenService.setTokens({
      accessToken: 'expired-access-token',
      refreshToken: 'invalid-refresh-token',
      idToken: 'mock-id-token',
      expiresAt: Date.now() - 10000
    });

    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    await userEvent.click(screen.getByRole('button', { name: /logout/i }));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
    });

    await waitFor(() => {
      expect(tokenService.getAccessToken()).toBeNull();
      expect(tokenService.getRefreshToken()).toBeNull();
    });
  });

  it('multi-tab: login in one tab syncs to another', async () => {
    // TODO: An update to AuthProvider inside a test was not wrapped in act(...).

    // Simulate login in one tab
    const tokenService = new TokenService();

    tokenService.setTokens({
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      idToken: 'mock-id-token',
      expiresAt: Date.now() + 60 * 60 * 1000
    });

    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    // Fire storage event
    window.dispatchEvent(new StorageEvent('storage', { key: 'auth_tokens' }));
    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('yes');
    });
  });

  // it.only('auth persistence: remains authenticated after remount', async () => {
  //   const tokenService = new TokenService();
  //
  //   tokenService.setTokens({
  //     accessToken: 'mock-access-token',
  //     refreshToken: 'mock-refresh-token',
  //     idToken: 'mock-id-token',
  //     expiresAt: Date.now() + 60 * 60 * 1000
  //   });
  //
  //   const { unmount, rerender } = customRender(
  //     <AuthProvider>
  //       <AuthStatus />
  //     </AuthProvider>
  //   );
  //
  //   await waitFor(() => {
  //     expect(screen.getByTestId('auth-status').textContent).toBe('yes');
  //   });
  //   // Unmount and remount
  //   unmount();
  //   rerender(
  //     <AuthProvider>
  //       <AuthStatus />
  //     </AuthProvider>
  //   );
  //
  //   await waitFor(() => {
  //     expect(screen.getByTestId('auth-status').textContent).toBe('yes');
  //   });
  // });

  it('api error: shows error message on network/server error (login)', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    const emailInput = screen.getByLabelText(/login-email/i);
    const passwordInput = screen.getByLabelText(/login-password/i);

    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123');
    await userEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(screen.getByTestId('auth-status').textContent).toBe('no');
      expect(
        (screen.getByTestId('auth-error').textContent ?? '').toLowerCase()
      ).toMatch(/network|error|server/);
    });
  });

  it('UX: disables forms and shows loading during async actions', async () => {
    customRender(
      <AuthProvider>
        <AuthStatus />
      </AuthProvider>
    );

    const emailInput = screen.getByLabelText(/login-email/i);
    const passwordInput = screen.getByLabelText(/login-password/i);

    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'password123');

    await userEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /login/i })).toBeDisabled();
    });

    expect(screen.getByTestId('auth-loading').textContent).toBe('loading');

    await waitFor(() => {
      expect(screen.getByTestId('auth-loading').textContent).toBe('idle');
    });
  });
});
