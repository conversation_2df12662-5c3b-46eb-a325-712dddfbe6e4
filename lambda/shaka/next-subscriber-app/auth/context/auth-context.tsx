'use client';

import React, {
  createContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import { useRouter } from 'next/navigation';
import { AuthTokens, TokenService } from '../tokens/token-service';
import { API_ENDPOINTS } from '../api/endpoints';
import { AxiosError } from 'axios';
import { createApiClient } from '@/lib/api-client-factory';

interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthContextValue {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  signup: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  apiClient: ReturnType<typeof createApiClient>;
}

export const AuthContext = createContext<AuthContextValue | undefined>(
  undefined
);

interface AuthProviderProps extends React.PropsWithChildren {
  tokenService?: TokenService;
  loginRedirectPath?: string;
  signupRedirectPath?: string;
}

interface State {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export const AuthProvider = ({
  children,
  loginRedirectPath = API_ENDPOINTS.dashboard,
  signupRedirectPath = '/login'
}: AuthProviderProps) => {
  const router = useRouter();
  // should it be injected per app ?
  const tokenService = useMemo(() => new TokenService(), []);
  const refreshLock = useRef<Promise<void> | null>(null);
  const [state, setState] = useState<State>({
    isAuthenticated: false,
    isLoading: true,
    error: null
  });

  const [apiClient] = useState(() => {
    return createApiClient({
      tokenService,
      refreshTokenFn: async () => {
        if (refreshLock.current) {
          return refreshLock.current;
        }
        return refreshToken();
      }
    });
  });

  // Helper to check auth status and update state
  const checkAuthStatus = () => {
    const tokens = tokenService.getTokens();
    const isAuthenticated = tokens && !tokenService.isTokenExpired(tokens);
    setState((prev) => ({
      ...prev,
      isAuthenticated: !!isAuthenticated,
      isLoading: false
    }));
  };

  useEffect(() => {
    // is it correct to check for all tokens not just refresh ? - Paw
    const tokens = tokenService.getTokens();
    if (!tokens) {
      setState((prev) => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false
      }));
      return;
    }

    if (tokenService.isTokenExpired(tokens)) {
      if (tokens.refreshToken) {
        // Attempt to refresh token automatically if access token is expired
        refreshToken();
      } else {
        setState((prev) => ({
          ...prev,
          isAuthenticated: false,
          isLoading: false
        }));
      }
      return;
    }

    setState((prev) => ({
      ...prev,
      isAuthenticated: true,
      isLoading: false
    }));

    // Listen for storage events (multi-tab sync)
    const handleStorage = (event: StorageEvent) => {
      if (event.key && event.key.includes('token')) {
        checkAuthStatus();
      }
    };
    window.addEventListener('storage', handleStorage);
    return () => {
      window.removeEventListener('storage', handleStorage);
    };
  }, []);

  // For tests purposes only - PAW - delete soon
  useEffect(() => {
    console.log('[AuthProvider] useEffect runs: state changed', state);
  }, [state]);

  const login = async ({
    email,
    password
  }: LoginCredentials): Promise<boolean> => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));
    // console.log('[AuthProvider] login called', { email });
    try {
      const response = await apiClient.post<{ tokens: AuthTokens }>(
        API_ENDPOINTS.auth.login,
        { email, password },
        { skipAuth: true }
      );
      // console.log('[AuthProvider] login success', response.tokens);
      tokenService.setTokens(response.tokens);

      setState((prev) => ({
        ...prev,
        isAuthenticated: true,
        isLoading: false
      }));
      router.push(loginRedirectPath);

      return true;
    } catch (error: unknown) {
      let errorMessage = 'Login failed. Please try again.';
      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || errorMessage;
      }

      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      // console.log('[AuthProvider] login error', errorMessage, error);
      return false;
    }
  };

  const signup = async ({
    email,
    password
  }: LoginCredentials): Promise<boolean> => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));
    // console.log('[AuthProvider] signup called', { email });
    try {
      const response = await apiClient.post<{ tokens: AuthTokens }>(
        API_ENDPOINTS.auth.signup,
        { email, password },
        { skipAuth: true }
      );

      // console.log('[AuthProvider] signup success', response.tokens);
      tokenService.setTokens(response.tokens);
      setState((prev) => ({
        ...prev,
        isAuthenticated: true,
        isLoading: false
      }));

      router.push(loginRedirectPath);

      return true;
    } catch (error: unknown) {
      let errorMessage = 'Signup failed. Please try again.';
      if (error instanceof AxiosError) {
        errorMessage = error.response?.data?.message || errorMessage;
      }

      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));

      return false;
    }
  };

  const logout = async () => {
    try {
      const refreshToken = tokenService.getRefreshToken();
      // console.log('%cLOGOUT', 'color: green', '[AuthProvider] logout called', {
      //   refreshToken
      // });
      if (refreshToken) {
        await apiClient.post(API_ENDPOINTS.auth.logout, { refreshToken });
      }
    } catch (error) {
      console.error('[AuthProvider] Logout API call failed:', error);
    } finally {
      // console.log('%cError', 'color: red', '[AuthProvider] finally block run');

      tokenService.clearTokens();

      setState({
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
      router.push(signupRedirectPath);
    }
  };

  const refreshToken = async () => {
    if (!refreshLock.current) {
      refreshLock.current = (async () => {
        try {
          console.log('[AuthProvider] refreshToken: start');
          setState((prev) => ({ ...prev, isLoading: true, error: null }));

          const refreshTokenValue = tokenService.getRefreshToken();
          console.log('[AuthProvider] refreshToken called', {
            refreshTokenValue
          });

          if (!refreshTokenValue) throw new Error('No refresh token available');

          const data = await apiClient.post<{ tokens: AuthTokens }>(
            API_ENDPOINTS.auth.refresh,
            { refreshToken: refreshTokenValue },
            { skipAuth: true }
          );

          console.log('[AuthProvider] refreshToken success', data.tokens);
          tokenService.setTokens(data.tokens);

          setState({
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error) {
          console.log('[AuthProvider] refreshToken: ERROR', error);
          console.log('[AuthProvider] Clearing tokens due to refresh error');
          tokenService.clearTokens();

          setState({
            isAuthenticated: false,
            isLoading: false,
            error: 'Refresh token invalid or expired. Please log in again.'
          });
        } finally {
          console.log('[AuthProvider] refreshToken: finally');
          refreshLock.current = null;
        }
      })();
    } else {
      console.log('[AuthProvider] refreshToken: already running');
    }
    return refreshLock.current;
  };

  const contextValue = useMemo(
    () => ({
      ...state,
      apiClient,
      login,
      logout,
      refreshToken,
      signup
    }),
    [state, apiClient]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};
