'use client';

import React, { createContext, use } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { getStepFromPath } from '@/utils/helpers';
import { WizardContextValue, WizardProviderProps } from './wizard.types';

const WizardContext = createContext<WizardContextValue<any> | undefined>(
  undefined
);

export function WizardProvider<TStep extends string>({
  children,
  steps,
  stepRoutes
}: WizardProviderProps<TStep>) {
  const router = useRouter();
  const pathname = usePathname();

  const currentStep = getStepFromPath(stepRoutes, pathname);
  const currentStepNumber = currentStep ? steps.indexOf(currentStep) + 1 : 0;
  const currentIndex = currentStep ? steps.indexOf(currentStep) : -1;

  const goToNextStep = () => {
    if (currentIndex < steps.length - 1) {
      const next = steps[currentIndex + 1];
      router.push(stepRoutes[next]);
    }
  };

  const goBackToPreviousStep = () => {
    if (currentIndex > 0) {
      const prev = steps[currentIndex - 1];
      router.push(stepRoutes[prev]);
    }
  };

  return (
    <WizardContext.Provider
      value={{
        currentStepNumber,
        currentStep: steps[currentIndex],
        currentIndex,
        totalSteps: steps.length,
        goToNextStep,
        goBackToPreviousStep
      }}
    >
      {children}
    </WizardContext.Provider>
  );
}

export function useWizard<TStep extends string>() {
  const context = use(
    WizardContext as React.Context<WizardContextValue<TStep> | undefined>
  );
  if (!context) {
    throw new Error('useWizard must be used within a WizardProvider');
  }
  return context;
}
