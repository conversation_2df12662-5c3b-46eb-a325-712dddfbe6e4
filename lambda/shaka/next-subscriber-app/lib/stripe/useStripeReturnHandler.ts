'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { PaymentStatus, UseStripeReturnHandlerOptions } from './types';
import { useAuth } from '@/auth/hooks/use-auth';
import { AxiosError } from 'axios';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';

export function useStripeReturnHandler(options: UseStripeReturnHandlerOptions) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { apiClient, isAuthenticated } = useAuth();
  const [status, setStatus] = useState<PaymentStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const paymentIntentId = searchParams.get('payment_intent');
  const sessionId = searchParams.get('session_id');

  // better way of handling it REACT QUERY ?
  useEffect(() => {
    if (!isAuthenticated) {
      router.replace(ROUTES_CONFIG['payment-error'].path);
      return;
    }

    if (!paymentIntentId && !sessionId) {
      setLoading(false);
      setError(new Error('No payment information found'));
      router.push(options.onErrorRedirectUrl);
      return;
    }

    const checkStatus = async () => {
      try {
        // move to ENDPOINTS
        const endpoint = '/api/payment/session-status';
        let params = {};

        if (sessionId) {
          params = { session_id: sessionId };
        } else if (paymentIntentId) {
          params = { payment_intent: paymentIntentId };
        }

        const response = await apiClient.get(endpoint, { params });
        const result = response.data;

        setStatus(result.status || result.payment_status);

        if (result.status === 'succeeded' || result.payment_status === 'paid') {
          router.push(options.onSuccessRedirectUrl);
        } else {
          router.push(options.onErrorRedirectUrl);
        }
      } catch (err: unknown) {
        if (err instanceof AxiosError) {
          setError(err);
          router.push(options.onErrorRedirectUrl);
        }
      } finally {
        setLoading(false);
      }
    };

    checkStatus();
    // omnce u have api chceck if something needs to be added here
  }, []);

  return {
    status,
    loading,
    error
  };
}
