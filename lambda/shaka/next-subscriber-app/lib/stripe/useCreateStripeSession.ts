'use client';

import { useState } from 'react';
import { CreateSessionParams, SessionResponse } from './types';
import { useAuth } from '@/auth/hooks/use-auth';
import { AxiosError } from 'axios';
import { API_ENDPOINTS } from '@/auth/api/endpoints';

export function useCreateStripeSession() {
  // react query !!
  const { apiClient } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AxiosError | null>(null);

  console.log(error, 'useCreateStripeSession error');

  const createSession = async (
    params: CreateSessionParams
  ): Promise<SessionResponse> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.post(
        API_ENDPOINTS.stripe.createCheckoutSession('1', 'return')
        // params
      );
      const data = response.data;

      console.log(data, 'createSession response data');

      return {
        clientSecret: data.client_secret || data.clientSecret,
        sessionId: data.session_id || data.sessionId
      };
    } catch (err: unknown) {
      if (err instanceof AxiosError) {
        setError(err);
      }
      // is app recoverable ?
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    createSession,
    loading,
    error
  };
}
