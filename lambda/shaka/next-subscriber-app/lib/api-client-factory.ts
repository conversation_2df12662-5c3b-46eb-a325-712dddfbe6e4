import { AxiosClient } from './axios-client';
import { TokenService } from '@/auth/tokens/token-service';

interface CreateApiClientOptions {
  tokenService?: TokenService;
  timeout?: number;
  headers?: Record<string, string>;
  refreshTokenFn?: () => Promise<void>;
}

export function createApiClient(options: CreateApiClientOptions): AxiosClient {
  const {
    tokenService = new TokenService(),
    timeout,
    headers,
    refreshTokenFn
  } = options;

  return new AxiosClient(
    tokenService,
    {
      timeout,
      headers
    },
    refreshTokenFn
  );
}
