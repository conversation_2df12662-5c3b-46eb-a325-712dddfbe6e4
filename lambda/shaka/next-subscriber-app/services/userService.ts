// /**
//  * Service Layer Architecture
//
//  * The service layer sits between the hooks layer and the API layer, providing these benefits:
//  * types should live close to where they are used
//  **/
//
// export interface User {
//   id: string;
//   name: string;
//   email: string;
// }
//
// export interface CreateUserDto {
//   name: string;
//   email: string;
//   password: string;
// }
//
// export interface UpdateUserDto {
//   name?: string;
//   email?: string;
// }
//
// // how to solve that ?
// export const userService = {
//   getUsers: async (): Promise<User[]> => {
//     return await axiosClient.get<User[]>('/users');
//   }
// };
