"use client";

import { Navigation } from "@/app/navigation";
import { Button } from "@/components/Button";
import Input from "@/components/Input";
import { ReferralInputs, schema } from "@/schemas/referral";
import { yupResolver } from "@hookform/resolvers/yup";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import ResultBlock from "./ResultBlock";
import { applyReferral } from "@/api/subscription";
import { useSubscriber } from "@/hooks/useSubscriber";
import useLocalStorage, { LocalKey } from "@/hooks/useLocalStorage";
import withAuth from "@/hoc/withAuth";
import { useRouter } from "next/navigation";

function ReferralForm() {
  const { subscriber, updateSubscriber } = useSubscriber();
  const { setLSValue, getLSValue } = useLocalStorage(LocalKey.PASSED_REFERRAL);
  const router = useRouter();

  const plan = subscriber?.plans?.[0];

  const [success, setSuccess] = useState(subscriber?.referral_applied);
  const [failed, setFailed] = useState(false);

  const formScreen = !(failed || success);

  const { handleSubmit, control, resetField } = useForm<ReferralInputs>({
    resolver: yupResolver(schema),
  });

  const onSubmit = (data: ReferralInputs) => {
    applyReferral({ code: data.code })
      .then((data) => {
        updateSubscriber(data);
        if (data.referral_applied) {
          setSuccess(true);
          return;
        }
        onSkippedCode();
      })
      .catch((error) => {
        console.error(error);
        setFailed(true);
      });
  };

  const onRetry = () => {
    setFailed(false);
    setSuccess(false);
    resetField("code");
  };

  const onSkippedCode = () => {
    setLSValue("true");
  };

  useEffect(() => {
    const isPassedReferral = getLSValue();
    if (isPassedReferral === "true") {
      router.push(Navigation.PAYMENT);
    }
  }, [getLSValue, router]);

  useEffect(() => {
    if (plan) {
      router.push(Navigation.DASHBOARD);
    }
  }, [plan, router]);

  return (
    <>
      {formScreen && (
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="h-full flex flex-col justify-between relative"
        >
          <div className="max-w-[340px] w-full m-auto flex-grow md:flex flex-col justify-center">
            <Controller
              control={control}
              name="code"
              render={({ field, fieldState: { error } }) => (
                <div className="relative m-auto w-full">
                  <Input
                    {...field}
                    error={error?.message}
                    errorAbsolute
                    name="code"
                    type="text"
                    title="Referral code"
                  />
                </div>
              )}
            />
          </div>

          <div className="max-md:fixed bottom-4 max-md:w-[90%]">
            <div className="flex flex-col gap-4 md:flex-row justify-center md:justify-between mt-6 md:gap-10">
              <div className="w-full md:w-1/2">
                <Link href={Navigation.PAYMENT}>
                  <Button
                    type="button"
                    variant="filled-gray"
                    onClick={onSkippedCode}
                  >
                    I don’t have a referral code
                  </Button>
                </Link>
              </div>

              <div className="w-full md:w-1/2">
                <Button type="submit" variant="filled">
                  Continue
                </Button>
              </div>
            </div>
          </div>
        </form>
      )}

      {success && (
        <ResultBlock
          title="Nice! You just got £5 off"
          alt="referral-success"
          imageSrc="/images/referral-success.png"
          description={
            <p>
              This will be added to your wallet once signed in. Make sure you
              refer your friends after you sign up. You will earn additional{" "}
              <span className="font-bold">£5 off your bill</span> for each
              referral.
            </p>
          }
        />
      )}

      {failed && (
        <ResultBlock
          title="Sorry that code is no longer valid"
          alt="referral-failed"
          imageSrc="/images/referral-failed.png"
          onRetry={onRetry}
          description={
            <p>
              Don’t worry though - refer your friends and you will earn £5 off
              your bill for each.
            </p>
          }
        />
      )}
    </>
  );
}

export default withAuth(ReferralForm);
