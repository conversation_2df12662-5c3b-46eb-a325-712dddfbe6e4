"use client";

import { useSubscriber } from "@/hooks/useSubscriber";
import { loginSso } from "@/api/auth";
import Loading from "@/components/Loading";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Navigation } from "../navigation";


function CallbackCommonPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const { login, loggedIn } = useAuth();
  const [tokensRecorded, setTokensRecorded] = useState(false);
  const { subscriber } = useSubscriber();
  
  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const params = new URLSearchParams(window.location.search);
        const code = params.get("code");

        if (!code) {
          setError("Authorization code not found");
          setLoading(false);
          return;
        }

        const tokens = await loginSso({
          code
        });

        console.log('got tokens', tokens, login);
        login(tokens);
        console.log('login called');

        setTokensRecorded(true);
      } catch (err) {
        console.log('login failed');
        setError("Failed to handle authentication callback");
        console.error(err);
      } finally {
        console.log('unloading');
        setLoading(false);
      }
    };

    handleAuthCallback();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  useEffect(() => {
    console.log(subscriber, tokensRecorded, subscriber?.plans, loggedIn);
    if (loggedIn) {
      if (subscriber?.plans) {
        console.log('dash');
        router.push(Navigation.DASHBOARD);
      }
      else {
        console.log('ref');
        router.push(Navigation.REFERRAL);
      }
    }
  }, [subscriber, tokensRecorded, loggedIn]);
  
  if (!loading && !error) {
    return null;
  }

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Error: {error}</p>
    </div>
  );
}

export default function CallbackPage() {
  return (
    <AuthProvider>
      <CallbackCommonPage />
    </AuthProvider>
  );
}
