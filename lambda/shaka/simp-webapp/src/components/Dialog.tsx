import {
  DialogBackdrop,
  Dialog as DialogModal,
  DialogPanel,
  DialogTitle,
} from "@headlessui/react";
import Image from "next/image";
import { PropsWithChildren, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

export default function Dialog({
  open,
  onClose,
  title,
  children,
  autoWidth,
  hideCloseButton,
}: PropsWithChildren<{
  title?: string | ReactNode;
  open: boolean;
  onClose: () => void;
  autoWidth?: boolean;
  hideCloseButton?: boolean;
}>) {
  return (
    <DialogModal
      open={open}
      as="div"
      className="relative z-10 focus:outline-none"
      onClose={onClose}
    >
      <DialogBackdrop className="fixed inset-0 bg-black/30 backdrop-blur-sm" />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-4">
          <DialogPanel
            transition
            className={twMerge(
              "w-full max-w-[740px] rounded-xl bg-[#252525]/80 p-12 px-7 md:px-12 backdrop-blur-[20px] relative",
              autoWidth && "w-auto"
            )}
          >
            {!hideCloseButton && (
              <button
                className="bg-[#D9D9D9]/20 rounded-full size-7 flex justify-center items-center absolute top-5 right-5"
                onClick={onClose}
              >
                <Image
                  src="/icons/cross.svg"
                  width={12}
                  height={12}
                  alt="close-icon"
                />
              </button>
            )}
            {title && (
              <DialogTitle as="h3" className="text-3xl font-regular text-white max-md:mt-3">
                {title}
              </DialogTitle>
            )}
            <div
              className={twMerge(
                "max-h-[80svh] max-md:overflow-y-auto pr-3 -mr-3",
                title && "mt-6"
              )}
            >
              {children}
            </div>
          </DialogPanel>
        </div>
      </div>
    </DialogModal>
  );
}
