import Image from "next/image";
import { twMerge } from "tailwind-merge";

export default function AppStoreButtons({
  withBorder,
  noWrap,
  size = "large",
}: {
  withBorder?: boolean;
  noWrap?: boolean;
  size?: "large" | "small" | "medium";
}) {
  const { width, height } =
    size === "small"
      ? { width: 60, height: 20 }
      : size === "medium"
      ? { width: 120, height: 33 }
      : { width: 195, height: 54 };

  return (
    <div
      className={twMerge(
        "flex flex-col md:flex-row gap-6 items-center",
        noWrap && "flex-row",
        size === "small" && "gap-2"
      )}
    >
      <a
        href="https://apps.apple.com/"
        target="_blank"
        rel="noopener noreferrer"
      >
        <Image
          src="/images/app-store.png"
          width={width}
          height={height}
          alt="Download on the App Store"
          className={twMerge(
            "border-[#868686] max-md:border rounded-lg",
            size !== "large" && "rounded-full",
            withBorder && "border"
          )}
        />
      </a>
      <a href="/" target="_blank" rel="noopener noreferrer">
        <Image
          src="/images/google-play.png"
          width={width}
          height={height}
          alt="Get it on Google Play"
          className={twMerge(
            "border-[#868686] max-md:border rounded-lg",
            size !== "large" && "rounded-full",
            withBorder && "border"
          )}
        />
      </a>
    </div>
  );
}
