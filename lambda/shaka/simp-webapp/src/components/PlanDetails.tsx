"use client";
import React, { useState } from "react";
import Image from "next/image";
import Dialog from "./Dialog";
import { Button } from "./Button";
import { Navigation } from "@/app/navigation";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/AuthContext";

const features = [
  {
    icon: "/icons/infinity.svg",
    text: (
      <>
        <span className="font-bold">Unlimited</span> UK data (up to 650GB),
        texts and minutes
      </>
    ),
  },
  {
    icon: "/icons/plane.svg",
    text: (
      <>
        <span className="font-bold">Free</span> global roaming - 20GB in Europe, 5GB globally
        and always-on
      </>
    ),
  },
  {
    icon: "/icons/spins-gray.svg",
    text: (
      <>
        <span className="font-bold">Spins</span> tickets weekly
      </>
    ),
  },
  {
    icon: "/icons/coin-gray.svg",
    text: (
      <>
        <span className="font-bold">+200</span> points weekly
      </>
    ),
  },
  {
    icon: "/icons/wi-fi.svg",
    text: (
      <>
        <span className="font-bold">5G</span> network access
      </>
    ),
  },
  {
    icon: "/icons/hearts.svg",
    text: (
      <>
        <span className="font-bold">£5</span> friend referral bonus
      </>
    ),
  },
];

const PlanDetails = () => {
  const router = useRouter();
  const { loggedIn } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  const closeDialog = () => {
    setIsOpen(false);
  };

  const onSelect = () => {
    closeDialog();
    router.push(Navigation.SIGN_UP);
  };

  return (
    <>
      <p className="text-[#868686] text-xs md:text-sm ">
        <button className="underline" onClick={() => setIsOpen(true)}>
          Terms and Conditions
        </button>{" "}
        apply.
      </p>

      <Dialog title="" open={isOpen} onClose={closeDialog} autoWidth>
        <div className="text-white px-3 max-w-xs pb-10 pt-8">
          <div className="flex justify-between items-start">
            <h3 className="text-2xl text-[#FDFE00] w-1/2">
              Just <span className="font-bold">Unlimited</span>
            </h3>
            <div className="text-right">
              <p className="text-4xl font-bold">£20</p>
              <p className="text-sm">/month</p>
            </div>
          </div>

          <div className="mt-9">
            <h4 className="text-[#868686] uppercase text-xs font-mono mb-6">
              Plan Includes
            </h4>
            <ul className="mt-3 space-y-4">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center gap-5">
                  <Image src={feature.icon} alt="icon" width={22} height={22} />
                  <span className="text-sm mt-0.5">{feature.text}</span>
                </li>
              ))}
            </ul>
          </div>

          <p className="text-[#868686] text-xs mt-8 mb-14">
            Full{" "}
            <Link href={Navigation.TERMS} className="underline">
              Terms and Conditions
            </Link>{" "}
            apply.
          </p>
        </div>
        <div className="absolute left-1/2 bottom-10 w-full px-3 max-w-xs -translate-x-1/2">
          {loggedIn ? (
            <Button variant="filled-gray" onClick={closeDialog}>
              Go back
            </Button>
          ) : (
            <Button variant="filled" onClick={onSelect}>
              Choose this plan
            </Button>
          )}
        </div>
      </Dialog>
    </>
  );
};

export default PlanDetails;
