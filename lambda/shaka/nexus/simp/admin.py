from django.contrib import admin
from django import forms
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.conf import settings
from .models import (
    Draw, Prize, PrizeTemplate, StaticFile,
    Onboarding, Alert, AlertTarget, Notification
)

@admin.register(Draw)
class DrawAdmin(admin.ModelAdmin):
    list_display = ('name', 'client', 'start_datetime', 'end_datetime', 'base_pool_size', 'new_subscriber_buffer', 'status')
    list_filter = ('status', 'client', 'start_datetime', 'end_datetime')
    search_fields = ('name', 'client__name')

@admin.register(Prize)
class PrizeAdmin(admin.ModelAdmin):
    list_display = ('name', 'draw', 'claimed_by', 'datetime_claimed')
    list_filter = ('draw', 'datetime_claimed')
    search_fields = ('name', 'draw__name', 'claimed_by__name')
    raw_id_fields = ('claimed_by',)

@admin.register(PrizeTemplate)
class PrizeTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_empty')
    search_fields = ('name', 'description')
    list_filter = ('is_empty',)


class StaticFileForm(forms.ModelForm):
    file_upload = forms.FileField(required=False, help_text="Upload a new file")

    class Meta:
        model = StaticFile
        fields = ['name', 'content_type']  # Exclude 'data' since it's not editable

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.cleaned_data.get("file_upload"):
            uploaded_file = self.cleaned_data["file_upload"]
            instance.data = uploaded_file.read()
            instance.content_type = uploaded_file.content_type
        if commit:
            instance.save()
        return instance


@admin.register(StaticFile)
class StaticFileAdmin(admin.ModelAdmin):
    form = StaticFileForm
    list_display = ['name', 'content_type', 'created_at', 'link']

    def link(self, obj):
        url = reverse('serve_static_file', args=[obj.name])
        return mark_safe(f'<a href="{url}">{settings.SITE_URL}{url}</a>')

@admin.register(Onboarding)
class OnboardingDetailsAdmin(admin.ModelAdmin):
    raw_id_fields = ('subscriber',)

@admin.register(Alert)
class AlertAdmin(admin.ModelAdmin):
    list_display = ('title', 'enabled', 'alert_level', 'display_after')
    list_filter = ('enabled', 'alert_level')
    search_fields = ('title', 'message')
    raw_id_fields = ('targeted_subscribers', 'dismissed_by')

@admin.register(AlertTarget)
class AlertTargetAdmin(admin.ModelAdmin):
    raw_id_fields = ('alert', 'subscriber')

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('text', 'subscriber', 'created_at', 'seen', 'humanized_date')
    list_filter = ('seen', 'created_at')
    search_fields = ('text', 'subscriber__cognito_username')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'humanized_date')
    raw_id_fields = ('subscriber',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('subscriber')

    def humanized_date(self, obj):
        return obj.date_display
    humanized_date.short_description = 'Relative Time'
