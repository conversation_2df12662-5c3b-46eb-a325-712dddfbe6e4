from contextlib import contextmanager
import secrets
import logging
from django.db import models, transaction, connection
from django.utils import timezone
from django.conf import settings
from django.db.models import Q
from core.models import Client, Subscriber
from .draw_logic import run_simulated_draw

logger = logging.getLogger()

def get_random_number(exclusive_upper_bound):
    return secrets.randbelow(exclusive_upper_bound)


def is_using_postgres():
    return "postgresql" in settings.DATABASES["default"]["ENGINE"]


class Draw(models.Model):
    class DrawStatus(models.TextChoices):
        DRAFT = "draft", "Draft"
        RUNNING = "running", "Running"
        FINISHED = "finished", "Finished"

    name = models.CharField(max_length=255)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    start_datetime = models.DateTimeField()
    end_datetime = models.DateTimeField()
    base_pool_size = models.IntegerField(
        help_text="Excluding new subscriber buffer and respin buffer"
    )
    new_subscriber_buffer = models.IntegerField(
        default=0, help_text="Number of prizes to reserve for new subscribers"
    )
    respin_buffer = models.IntegerField(
        default=0, help_text="Max number of respins to allow"
    )
    image_link = models.URLField(null=True, blank=True)
    status = models.CharField(
        max_length=10, choices=DrawStatus.choices, default=DrawStatus.DRAFT
    )

    @property
    def total_pool_size(self):
        return self.base_pool_size + self.respin_buffer + self.new_subscriber_buffer

    @property
    def prizes_claimed(self):
        return self.prizes.filter(claimed_by__isnull=False).count()

    @property
    def prizes_not_claimed(self):
        return self.total_pool_size - self.prizes_claimed

    @property
    def start_datetime_timestamp(self):
        return int(self.start_datetime.timestamp())  # pylint: disable=no-member

    @property
    def end_datetime_timestamp(self):
        return int(self.end_datetime.timestamp())  # pylint: disable=no-member

    def __str__(self):
        return self.name

    def start_draw(self, entries):
        with self.lock() as draw:
            if draw.status != Draw.DrawStatus.DRAFT:
                raise RuntimeError("Draw must be in draft status to start")
            draw.status = Draw.DrawStatus.RUNNING
            draw.save()
        run_simulated_draw(self, entries)

    @contextmanager
    def lock(self):
        with transaction.atomic():
            draw = Draw.objects.select_for_update().get(pk=self.pk)
            yield draw

    def reset(self):
        with self.lock() as draw:
            draw.status = Draw.DrawStatus.DRAFT
            draw.save()
            self.prizes.all().update(claimed_by=None, datetime_claimed=None)

    def draw_a_prize(self, subscriber, redraw_count=0):
        draw = self
        claimed_prize = None
        try:
            rng = get_random_number(draw.total_pool_size)
            prize = draw.prizes.order_by('-id')[rng]
            def try_again():
                logger.info("Subscriber %s drew a claimed prize, respinning %s", subscriber, redraw_count)
                print(f"Subscriber {subscriber} drew a claimed prize, respinning {redraw_count}")
                return self.draw_a_prize(subscriber, redraw_count + 1)
            if prize.is_claimed:
                try_again()
            else:
                with prize.lock() as locked_prize:
                    if locked_prize.is_claimed:
                        must_try_again = True
                    else:
                        prize.claim(subscriber)
                        logger.info("Subscriber %s drew prize %s", subscriber, prize.name)
                        print(f"Subscriber {subscriber} drew prize {prize.name}")
                        claimed_prize = prize
                        must_try_again = False
                if must_try_again:
                    try_again()
        except Exception as e:  # pylint: disable=broad-except
            print(e)
            raise
        return claimed_prize


class PrizeTemplate(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    image_link = models.URLField(null=True, blank=True)
    is_empty = models.BooleanField(default=False, db_index=True)

    def __str__(self):
        return self.name


class Prize(models.Model):
    draw = models.ForeignKey(Draw, on_delete=models.CASCADE, related_name="prizes")
    claimed_by = models.ForeignKey(Subscriber, on_delete=models.CASCADE, null=True, blank=True)
    datetime_claimed = models.DateTimeField(null=True, blank=True)
    prize_template = models.ForeignKey(
        PrizeTemplate, on_delete=models.CASCADE, db_index=True
    )

    @property
    def is_empty(self):
        return self.prize_template.is_empty

    @property
    def name(self):
        return self.prize_template.name

    @property
    def num_respins(self):
        if "respin" in self.name.lower():
            return int(self.name.split(" ", maxsplit=1)[0])
        else:
            return 0

    @property
    def is_claimed(self):
        return self.claimed_by is not None

    def claim(self, claimed_by):
        self.claimed_by = claimed_by
        self.datetime_claimed = timezone.now()
        self.save()

    def __str__(self):
        return self.name

    @property
    def datetime_claimed_timestamp(self):
        return int(self.datetime_claimed.timestamp()) if self.datetime_claimed else None

    @contextmanager
    def lock(self):
        with transaction.atomic():
            if is_using_postgres():
                with connection.cursor() as cursor:
                    cursor.execute("SET TRANSACTION ISOLATION LEVEL REPEATABLE READ")
                    prize = Prize.objects.select_for_update().get(pk=self.pk)
                    yield prize
            else:
                prize = Prize.objects.select_for_update().get(pk=self.pk)
                yield prize

    class Meta:
        ordering = ["id"]
        indexes = [
            models.Index(fields=["draw", "claimed_by"]),
        ]


class StaticFile(models.Model):
    name = models.CharField(max_length=255, unique=True)
    content_type = models.CharField(max_length=255, help_text="MIME type of the file")
    data = models.BinaryField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class Onboarding(models.Model):
    subscriber = models.OneToOneField(
        Subscriber, on_delete=models.CASCADE, related_name="onboarding_details"
    )
    onboarding_details = models.JSONField(blank=True, default=dict)

    def __str__(self):
        return f"{self.subscriber} onboarding"

class AlertQuerySet(models.QuerySet):
    def for_subscriber(self, subscriber):
        display_query = Q(display_after__isnull=True) | Q(
            display_after__lt=timezone.now()
        )
        subscriber_query = Q(targeted_subscribers__isnull=True) | Q(
            targeted_subscribers=subscriber
        )
        return self.filter(
            display_query, subscriber_query, client=subscriber.client, enabled=True
        ).exclude(dismissed_by=subscriber)

class Alert(models.Model):
    class AlertLevel(models.TextChoices):
        RED = "red", "Red"
        AMBER = "amber", "Amber"
        GREEN = "green", "Green"

    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    message = models.TextField(blank=True, null=False, default="")
    targeted_subscribers = models.ManyToManyField(
        Subscriber, through="AlertTarget", related_name="targeted_alerts"
    )
    dismissed_by = models.ManyToManyField(
        Subscriber, related_name="dismissed_alerts", blank=True
    )
    created = models.DateTimeField(auto_now_add=True)
    display_after = models.DateTimeField(null=True, blank=True)
    enabled = models.BooleanField(default=False)
    alert_level = models.CharField(max_length=10, choices=AlertLevel.choices)

    objects = models.Manager.from_queryset(AlertQuerySet)()

    def __str__(self):
        return self.title

    def mark_as_dismissed(self, subscriber):
        self.dismissed_by.add(subscriber)

class AlertTarget(models.Model):
    alert = models.ForeignKey(Alert, on_delete=models.CASCADE)
    subscriber = models.ForeignKey(Subscriber, on_delete=models.CASCADE)

    def __str__(self):
        return f"Alert: {self.alert} - Subscriber: {self.subscriber}"

class Notification(models.Model):
    subscriber = models.ForeignKey(
        'core.Subscriber',
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    text = models.CharField(max_length=255)
    created_at = models.DateTimeField(default=timezone.now)
    seen = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']
    @property
    def date_display(self):
        diff = timezone.now() - self.created_at
        total_seconds = diff.total_seconds()
        if total_seconds < 120:
            return "just now"
        elif total_seconds < 3600:
            minutes = int(total_seconds / 60)
            return f"{minutes} minutes ago"
        elif total_seconds < 86400:
            hours = int(total_seconds / 3600)
            return f"{hours} hours ago" if hours > 1 else "1 hour ago"
        elif total_seconds < 604800:
            days = int(total_seconds / 86400)
            return f"{days} days ago" if days > 1 else "1 day ago"
        else:
            weeks = int(total_seconds / 604800)
            return f"{weeks} weeks ago" if weeks > 1 else "1 week ago"
    def mark_as_seen(self):
        self.seen = True
        self.save(update_fields=['seen'])
    def __str__(self):
        return f"{self.text} ({self.subscriber})"
