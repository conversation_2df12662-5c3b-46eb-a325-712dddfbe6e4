import unittest
from datetime import datetime, timedelta
import calendar
from contextlib import contextmanager
from decimal import Decimal
import pytz
from dateutil.relativedelta import relativedelta
from rest_framework import status
from rest_framework.test import APIClient
from core.test_utils import NexusTestCase
from core.tests.factories import ClientFactory, SubscriptionFactory, PlanChangeFactory, PlanFactory, SubscriberFactory
from core.models import PlanChange, Referral
from subscriber_app.views import BearerTokenAuthentication

class MonkeyPatchSubscriberAuth:
    def __init__(self, subscriber):
        self.subscriber = subscriber
        self.old_has_permission = None

    def __enter__(self):
        self.old_has_permission = getattr(BearerTokenAuthentication, 'has_permission')
        setattr(BearerTokenAuthentication, 'has_permission', self.has_permission)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        setattr(BearerTokenAuthentication, 'has_permission', self.old_has_permission)

    def has_permission(self, request, _):
        request.access_token = ''
        request.cognito_username = self.subscriber.cognito_username
        return True


class SubscriberAppTestCase(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client)

    @contextmanager
    def forced_auth_subscriber(self):
        with MonkeyPatchSubscriberAuth(self.subscriber):
            yield

    def get(self, path):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.get(f'/s/api/v1/{self.client.obfuscated_id}{path}')
        return response

    def post(self, path, data):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.post(f'/s/api/v1/{self.client.obfuscated_id}{path}', data, format='json')
        return response

class SubscribedSubscriberAppTestCase(SubscriberAppTestCase):
    def setUp(self):
        super().setUp()
        self.subscription = SubscriptionFactory(using=True, subscriber=self.subscriber)


class SubscriberDetailTests(SubscribedSubscriberAppTestCase):
    stripe_backend = 'fake'

    def setUp(self):
        super().setUp()
        self.scenario.setup_billing_for_client(self.client)

    def test_subscriber_detail(self):
        response = self.get('/data/subscriber/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        assert response.json()['plans']

    def test_next_bill_date_epoch(self):
        response = self.get('/data/subscriber/')
        uk_tz = pytz.timezone('Europe/London')
        start_of_next_month = self.fake_services.stripe.current_time.astimezone(uk_tz).replace(day=1, hour=0, minute=0, second=0, microsecond=0) + relativedelta(months=1)
        assert response.json()['plans'][0]['next_bill_date_epoch'] == int(start_of_next_month.timestamp())

def is_near_end_of_month():
    now = datetime.utcnow()
    last_day = calendar.monthrange(now.year, now.month)[1]
    end_of_month = datetime(now.year, now.month, last_day, 23, 59, 59)
    return now >= (end_of_month - timedelta(hours=4))

@unittest.skipIf(is_near_end_of_month(), "Skipping tests near the end of the month.")  # I'm sorry
class PlanChangeTests(SubscribedSubscriberAppTestCase):
    def get_plan_from_detail(self):
        response = self.get('/data/subscriber/')
        data = response.json()
        assert len(data['plans']) == 1
        plan = data['plans'][0]
        return plan

    def test_subscriber_detail_handles_no_plan_change(self):
        plan = self.get_plan_from_detail()
        assert plan['can_upgrade']
        assert plan['can_downgrade']
        assert plan['can_cancel']
        assert not plan['can_cancel_change']
        assert plan['plan_changes'] == []

    def test_subscriber_detail_handles_upgrade(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert not plan['can_cancel_change']
        assert plan['plan_changes'][0]['id'] == plan_change.pk

    def test_subscriber_detail_handles_completed_upgrade(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription, status=PlanChange.Status.COMPLETE)
        plan = self.get_plan_from_detail()
        assert plan['can_upgrade']
        assert plan['can_downgrade']
        assert plan['can_cancel']
        assert not plan['can_cancel_change']
        assert plan['plan_changes'][0]['id'] == plan_change.pk

    def test_subscriber_detail_can_cancel_downgrade(self):
        PlanChangeFactory(downgrade=True, subscription=self.subscription, status=PlanChange.Status.IN_PROGRESS)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert plan['can_cancel_change']

    def test_subscriber_detail_handles_locked_downgrade(self):
        PlanChangeFactory(downgrade=True, subscription=self.subscription, status=PlanChange.Status.LOCKED)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert not plan['can_cancel_change']

    def test_subscriber_detail_handles_cancel_in_progress(self):
        plan_change = PlanChangeFactory(downgrade=True, subscription=self.subscription, status=PlanChange.Status.IN_PROGRESS)
        PlanChangeFactory(cancel_change=True, subscription=self.subscription, status=PlanChange.Status.IN_PROGRESS, target_plan_change=plan_change)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert not plan['can_cancel_change']

    def test_can_trigger_upgrade(self):
        plan = self.get_plan_from_detail()
        target_plan = PlanFactory(client=self.client, top_tier=True)
        assert plan['can_upgrade']
        response = self.post('/subscription/plan-change/', {'change_type': 'upgrade', 'target_plan_id': target_plan.id})
        assert response.status_code == status.HTTP_201_CREATED
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        plan_change = plan['plan_changes'][0]
        assert plan_change['change_type'] == 'upgrade'
        assert plan_change['status'] == 'in_progress'

    def test_can_get_plan_change_detail(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription)
        response = self.get(f'/subscription/plan-change/{plan_change.pk}/')
        assert response.status_code == status.HTTP_200_OK
        assert response.json()['id'] == plan_change.pk

    def test_can_get_plan_change_list(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription)
        response = self.get('/subscription/plan-change/')
        assert response.status_code == status.HTTP_200_OK
        assert response.json()[0]['id'] == plan_change.pk


class PlanSignUpTests(SubscriberAppTestCase):
    stripe_backend = 'fake'

    def setUp(self):
        super().setUp()
        self.scenario.setup_billing_for_client(self.client)

    def test_billing_anchor_is_set_correctly(self):
        plan = PlanFactory(client=self.client)
        response = self.post(f'/plans/sign-up/{plan.pk}/', {})
        assert response.status_code == status.HTTP_200_OK
        session = self.fake_services.stripe.get_session(response.json()['session_id'])
        uk_tz = pytz.timezone('Europe/London')
        start_of_next_month = self.fake_services.stripe.current_time.astimezone(uk_tz).replace(day=1, hour=0, minute=0, second=0, microsecond=0) + relativedelta(months=1)
        assert session['subscription_data']['billing_cycle_anchor'] == int(start_of_next_month.timestamp())

class ReferralAPITests(SubscribedSubscriberAppTestCase):
    def test_get_referral_data(self):
        response = self.get('/data/referral/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('referral_code', response.json())
        self.assertIn('referral_credit', response.json())
        self.assertIn('referrals', response.json())

    def test_record_new_referral_increases_credit(self):
        referrer = SubscriberFactory()
        new_subscriber = SubscriberFactory()
        initial_credit = Decimal(referrer.referral_credit)
        credit_amount = Decimal('10.00')

        referrer.record_new_referral(new_subscriber, credit_amount)

        referrer.refresh_from_db()
        self.assertEqual(referrer.referral_credit, initial_credit + credit_amount)
        self.assertEqual(Referral.objects.count(), 1)
        referral = Referral.objects.first()
        self.assertEqual(referral.referrer, referrer)
        self.assertEqual(referral.referred_subscriber, new_subscriber)
        self.assertEqual(referral.referral_code, referrer.referral_code)
        self.assertEqual(referral.credit_applied, credit_amount)

    def test_get_referral_data_verify(self):
        referrer = SubscriberFactory()
        new_subscriber = SubscriberFactory()
        credit_amount = Decimal('10.00')

        referrer.record_new_referral(new_subscriber, credit_amount)
        referrer.refresh_from_db()
        new_subscriber.refresh_from_db()
        self.subscriber = referrer
        self.client = referrer.client

        response = self.get('/data/referral/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('referral_code', response.json())
        self.assertIn('referral_credit', response.json())
        self.assertIn('referrals', response.json())
        self.assertEqual(len(response.json()['referrals']), 1)
        self.assertEqual(response.json()['referrals'][0]['name'], new_subscriber.name)
        self.assertEqual(response.json()['referrals'][0]['credit_applied'], str(credit_amount))
