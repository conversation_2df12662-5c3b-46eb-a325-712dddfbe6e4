from rest_framework import serializers

class PlanSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    bg = serializers.IntegerField()
    title = serializers.CharField(max_length=100)
    data = serializers.CharField()
    voice = serializers.CharField()
    sms = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    current_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    active = serializers.BooleanField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class ReferralSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    date = serializers.DateTimeField()
    credit_applied = serializers.DecimalField(max_digits=10, decimal_places=2)

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class SubscriberReferralSerializer(serializers.Serializer):
    referral_code = serializers.CharField()
    referral_credit = serializers.DecimalField(max_digits=10, decimal_places=2)
    referrals = ReferralSerializer(many=True)

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class ClientSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(max_length=100)
    branding = serializers.SerializerMethodField()
    perk_point_name_singular = serializers.CharField()
    perk_point_name_plural = serializers.CharField()
    plans = PlanSerializer(many=True)
    spn = serializers.CharField()
    sso_links = serializers.DictField()

    def get_branding(self, obj):
        return obj['branding']

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class PlanChangeSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    status = serializers.CharField()
    change_type = serializers.CharField()
    target_plan_id = serializers.IntegerField()
    target_plan_change_id = serializers.IntegerField()
    subscriber_status_display = serializers.CharField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class SubscriberBoltOnSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()
    data = serializers.DictField(required=False)
    voice = serializers.DictField(required=False)
    status = serializers.CharField()
    expires = serializers.DateField()
    type = serializers.CharField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class SubscriberRoamingBoltOnSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField()
    data = serializers.CharField(required=False)
    voice = serializers.CharField(required=False)
    sms = serializers.CharField(required=False)
    zone = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    country_code = serializers.CharField()
    valid_until = serializers.DateTimeField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class SubscriberEURoamingBoltOnSerializer(serializers.Serializer):
    day_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    days_used = serializers.IntegerField()
    days_total = serializers.IntegerField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class NotificationsSerializer(serializers.Serializer):
    show_number_transfer = serializers.BooleanField()
    show_number_porting_progress = serializers.BooleanField()
    show_update_apn = serializers.BooleanField()
    show_set_up_esim = serializers.BooleanField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class SubscriberPlanSerializer(serializers.Serializer):
    id =  serializers.IntegerField()
    bg = serializers.IntegerField()
    plan_id = serializers.IntegerField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    next_bill_amount = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    next_bill_date_epoch = serializers.IntegerField(required=False)
    potential_cancellation_date_epoch = serializers.IntegerField(required=False)
    title = serializers.CharField()
    sim_activation_status = serializers.CharField()
    sim_serial_fragment = serializers.CharField()
    data = serializers.DictField()
    voice = serializers.DictField()
    sms = serializers.DictField()
    subscription_status = serializers.CharField()
    pac_code_status = serializers.CharField()
    self_activated = serializers.BooleanField()
    can_upgrade = serializers.BooleanField()
    can_downgrade = serializers.BooleanField()
    can_cancel = serializers.BooleanField()
    can_cancel_change = serializers.BooleanField()
    plan_changes = PlanChangeSerializer(many=True)
    latest_plan_change = PlanChangeSerializer()
    sim_type = serializers.CharField(max_length=10, default='physical')
    bolt_ons = SubscriberBoltOnSerializer(many=True)
    roaming_bolt_on = SubscriberRoamingBoltOnSerializer()
    roaming_bolt_on_eu = SubscriberEURoamingBoltOnSerializer()
    notifications = NotificationsSerializer()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class RewardsSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField(max_length=100)
    days_left = serializers.IntegerField()
    img = serializers.CharField(required=False)

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class OnboardingDetailsSerializer(serializers.Serializer):
    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class SubscriberSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    email = serializers.EmailField()
    name = serializers.CharField()
    date_of_birth = serializers.DateField(required=False)
    phone_number = serializers.CharField()
    plans = SubscriberPlanSerializer(many=True)
    address = serializers.CharField()
    is_verified = serializers.BooleanField()
    join_date = serializers.DateTimeField()
    send_marketing = serializers.BooleanField()
    sim_fragment = serializers.CharField()
    referral_applied = serializers.BooleanField()
    points = serializers.IntegerField()
    referral_bonus = serializers.DecimalField(max_digits=10, decimal_places=2)
    next_rewards = RewardsSerializer(many=True)
    onboarding_details = OnboardingDetailsSerializer()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class BoltOnSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField(max_length=100)
    data = serializers.CharField(required=False)
    voice = serializers.CharField(required=False)
    price = serializers.DecimalField(max_digits=10, decimal_places=2)

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError

class RoamingBoltOnSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    title = serializers.CharField(max_length=100)
    data = serializers.CharField(required=False)
    voice = serializers.CharField(required=False)
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    sms = serializers.CharField(required=False)
    zone = serializers.CharField(required=False)
    valid_until = serializers.DateTimeField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError


class ESimSettingsSerializer(serializers.Serializer):
    qr_code = serializers.CharField()
    ios_settings = serializers.DictField()
    android_settings = serializers.DictField()

    def create(self, *args, **kwargs):
        raise RuntimeError

    def update(self, *args, **kwargs):
        raise RuntimeError
