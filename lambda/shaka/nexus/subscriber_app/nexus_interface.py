import hmac
import hashlib
import base64
import logging
import boto3
import jwt as pyjwt
from django.forms.models import model_to_dict
from django.utils import timezone
from django.conf import settings
from core.models import Client, ClientBranding, Subscriber, PACRequest, Referral
from core.utils import bytes_to_gb, get_stripe_client
from core.time_control import STRIPE_BILLING_TIME_CONTROL
from core.slack import send_slack_message
from core.emails import send_verification_email, send_port_in_requested_email, send_voucher_details,send_password_changed, send_cancel_subscription_email, send_forgot_password_email
from core.plan_change import upgrade_to_plan, downgrade_to_plan, cancel_plan_change, cancel_subscription
from core.sig import generate_code
from .subscriber_auth import generate_oauth_login_details, get_openid_user_params, generate_fresh_auth_tokens, refresh_our_custom_jwt_for_openid, get_tokens_from_cognito_code

logger = logging.getLogger()

def calculate_secret_hash(username, client_id, client_secret):
    message = username + client_id
    dig = hmac.new(str(client_secret).encode('utf-8'), msg=str(message).encode('utf-8'), digestmod=hashlib.sha256).digest()
    secret_hash = base64.b64encode(dig).decode()
    return secret_hash


# pylint: disable=too-many-public-methods
class NexusInterface:
    def __init__(self, client_id):
        try:
            lookup = {'pk': int(client_id), 'obfuscated_id':''}
        except ValueError:
            lookup = {'obfuscated_id': client_id}
        self.client = Client.objects.get(**lookup)

    def login_user(self, email, password):
        client = boto3.client('cognito-idp', region_name='eu-west-2')
        secret_hash = calculate_secret_hash(email, self.client.subscriber_user_pool_client_id, self.client.subscriber_user_pool_client_secret)
        try:
            # Authenticate user with Cognito
            response = client.initiate_auth(
                ClientId=self.client.subscriber_user_pool_client_id,
                AuthFlow='USER_PASSWORD_AUTH',
                AuthParameters={
                    'USERNAME': email,
                    'PASSWORD': password,
                    'SECRET_HASH': secret_hash,
                },
            )
        except client.exceptions.NotAuthorizedException as e:
            logger.info('Error logging in: %s', e)
            return None

        return response['AuthenticationResult']

    def change_user_password(self, access_token, old_password, new_password):
        client = boto3.client('cognito-idp', region_name='eu-west-2')

        try:
            client.change_password(
                PreviousPassword=old_password,
                ProposedPassword=new_password,
                AccessToken=access_token
            )
            return 'ok'
        except client.exceptions.InvalidPasswordException:
            return 'invalid-password'
        except client.exceptions.LimitExceededException:
            return 'limit-exceeded'
        except client.exceptions.NotAuthorizedException:
            return 'no-match'

    def refresh_token(self, cognito_username, refresh_token):
        if self.client.is_openid_subscriber_auth:
            return refresh_our_custom_jwt_for_openid(refresh_token, self.get_subscriber_by_cognito_username(cognito_username), self.client)
        else:
            client = boto3.client('cognito-idp', region_name='eu-west-2')
            secret_hash = calculate_secret_hash(cognito_username, self.client.subscriber_user_pool_client_id, self.client.subscriber_user_pool_client_secret)
            try:
                # Authenticate user with Cognito
                response = client.initiate_auth(
                    ClientId=self.client.subscriber_user_pool_client_id,
                    AuthFlow='REFRESH_TOKEN_AUTH',
                    AuthParameters={
                        'REFRESH_TOKEN': refresh_token,
                        'SECRET_HASH': secret_hash,
                    },
                )

            except client.exceptions.NotAuthorizedException as e:
                logger.info('Error refreshing token: %s', e)
                return None
            return response['AuthenticationResult']

    def forgot_password(self, email):
        subscriber = self.client.subscribers.get(email=email)
        send_forgot_password_email(email, self.client, subscriber.pk)
        return True

    def confirm_forgot_password(self, email, confirmation_code, new_password):
        subscriber = self.client.subscribers.get(email=email)
        client = boto3.client('cognito-idp', region_name='eu-west-2')
        if subscriber.forgot_password_attempts >= 10:
            subscriber.forgot_password_attempts += 1
            subscriber.save()
            return 'Too many attempts'
        if confirmation_code == subscriber.forgot_password_code:
            subscriber.forgot_password_code = ''
            subscriber.forgot_password_attempts = 0
            subscriber.save()
            try:
                client.admin_set_user_password(
                    UserPoolId=self.client.subscriber_user_pool_id,
                    Username=email,
                    Password=new_password,
                    Permanent=True
                )
            except client.exceptions.InvalidPasswordException:
                return 'invalid-password'
            else:
                return 'ok'
        else:
            print(confirmation_code, email, new_password, subscriber, subscriber.forgot_password_code)
            subscriber.forgot_password_attempts += 1
            subscriber.save()
            return 'code-mismatch'

    def sign_up_user(self, email, password, name='', date_of_birth=None, ):  # pylint: disable=too-many-arguments
        client = boto3.client('cognito-idp', region_name='eu-west-2')
        secret_hash = calculate_secret_hash(email, self.client.subscriber_user_pool_client_id, self.client.subscriber_user_pool_client_secret)
        try:
            response = client.sign_up(
                ClientId=self.client.subscriber_user_pool_client_id,
                Username=email,
                Password=password,
                SecretHash=secret_hash
            )
            client.admin_confirm_sign_up(
                UserPoolId=self.client.subscriber_user_pool_id,
                Username=email
            )
            stripe_client = get_stripe_client(self.billing_secret_key)
            customer = stripe_client.customers.create(params={'email': email})
            extra_args = {}
            if settings.FORCE_ESIM:
                extra_args['intended_sim_type'] = 'esim'
            subscriber = Subscriber.objects.create(
                email=email,
                join_date=timezone.now(),
                client=self.client,
                name=name or email,
                date_of_birth=date_of_birth,
                cognito_username=response['UserSub'],
                billing_subscriber_id=customer.id,
                verification_code=generate_code(),
                **extra_args
            )
            client.admin_update_user_attributes(
                UserPoolId=self.client.subscriber_user_pool_id,
                Username=subscriber.email,
                UserAttributes=[
                    {
                        'Name': 'email_verified',
                        'Value': 'true'
                    },
                ]
            )
            try:
                # Authenticate user with Cognito
                login_response = client.initiate_auth(
                    ClientId=self.client.subscriber_user_pool_client_id,
                    AuthFlow='USER_PASSWORD_AUTH',
                    AuthParameters={
                        'USERNAME': email,
                        'PASSWORD': password,
                        'SECRET_HASH': secret_hash,
                    },
                )
            except client.exceptions.NotAuthorizedException as e:
                logger.error('Error logging in: %s during sign up', e)
                return 'error'
            self.perform_additional_new_subscriber_setup(subscriber)
            return login_response['AuthenticationResult']
        except client.exceptions.InvalidPasswordException:
            return 'invalid-password'
        except client.exceptions.UsernameExistsException:
            return 'username-exists'

    def perform_additional_new_subscriber_setup(self, subscriber):
        if not subscriber.client.provider.is_demo:
            send_slack_message(f'New subscriber (not paid yet) {subscriber.email} for {subscriber.client.name}: [link in nexus](https://nexus.shaka.tel/admin/core/subscriber/{subscriber.pk}/change/)')
        if subscriber.verification_code:
            send_verification_email(subscriber.email, self.client, subscriber.pk)


    def get_client_data(self):
        base_sso_url = f'{self.client.subscriber_user_pool_domain}/oauth2/authorize?client_id={self.client.subscriber_user_pool_client_id}&redirect_uri={self.client.subscriber_webapp_url}/auth&response_type=code&scope=email+openid+phone+profile&identity_provider='
        return {
            "id": self.client.id,
            "name": self.client.name,
            "branding": model_to_dict(self.client.branding) if ClientBranding.objects.filter(client=self.client).exists() else {},
            'perk_point_name_singular': self.client.perk_point_name_singular,
            'perk_point_name_plural': self.client.perk_point_name_plural,
            'spn': self.client.spn,
            'sso_links': {
                'google': f'{base_sso_url}Google',
                'facebook': None,
                'apple': f'{base_sso_url}SignInWithApple'
            }
        }

    def get_all_plans(self):
        for plan in self.client.plans.all().order_by('-price'):
            yield {
                'title': plan.name,
                'active': plan.is_active,
                'price': plan.price,
                'current_price': plan.current_price_for_new_subscribers,
                'data': plan.data_limit_display_qty,
                'sms': plan.sms_limit_display_qty,
                'bg': plan.bg,
                'voice': plan.voice_limit_display_qty,
                'billing_plan_id': plan.billing_plan_id,
                'id': plan.id
            }

    @property
    def billing_secret_key(self):
        return self.client.payment_integration.secret_credentials

    @property
    def subscriber_webapp_url(self):
        return self.client.subscriber_webapp_url

    def get_plan(self, plan_id):
        for plan in self.get_all_plans():
            if int(plan['id']) == int(plan_id):
                return plan
        return None

    def get_plan_model(self, plan_id):
        return self.client.plans.all().get(pk=plan_id)

    def get_remaining_usage_this_billing_month(self, sim, msisdn, dimension):
        if dimension == 'data':
            usage = sim.get_data_usage_this_billing_month(msisdn)
            if usage:
                return bytes_to_gb(usage)
            else:
                return 0
        else:
            raise RuntimeError('Bad dimension, todo')

    def get_eu_roaming_bolt_on_object(self, subscription):
        return subscription.subscriber.client.eu_roaming_bolt_on if subscription else None

    def get_subscriber_data(self, cognito_username):
        subscriber = Subscriber.objects.filter(cognito_username=cognito_username, client=self.client).get()
        subscription = subscriber.subscriptions.first()
        if subscription:
            plan = subscription.latest_plan or subscription.intended_plan
            sim = subscription.latest_sim
            if sim:
                data_usage = self.get_remaining_usage_this_billing_month(sim, subscription.latest_msisdn, 'data')
            else:
                data_usage = 0
        else:
            plan = None
            sim = None
            data_usage = None
        roaming_purchase = subscription.bolt_on_purchases.roaming().active().first() if subscription else None
        if roaming_purchase:
            roaming_bolt_on = {
                'id': roaming_purchase.pk,
                'title': roaming_purchase.bolt_on.display_name,
                'data': roaming_purchase.data_gb_remaining,
                'voice': roaming_purchase.voice_minutes_remaining,
                'sms': roaming_purchase.sms_remaining,
                'price': roaming_purchase.price,
                'zone': roaming_purchase.roaming_zone,
                'country_code': roaming_purchase.country_code,
                'valid_until': roaming_purchase.expiry,
            }
        else:
            roaming_bolt_on = {}

        eu_roaming = self.get_eu_roaming_bolt_on_object(subscription)
        if eu_roaming:
            roaming_bolt_on_eu = {
                "day_price": eu_roaming.price,
                "days_used": subscription.eu_roaming_days_this_month,
                "days_total": subscription.total_available_eu_roaming_days,
            }
        else:
            roaming_bolt_on_eu = {}


        return {
            'id': subscriber.id,
            'email': subscriber.email,
            'name': subscriber.name,
            'onboarding_details': None,
            'date_of_birth': subscriber.date_of_birth,
            'join_date': subscriber.join_date,
            'phone_number': subscription.latest_msisdn if subscription else None,
            'is_verified': subscriber.is_verified,
            'send_marketing': subscriber.send_marketing,
            'sim_fragment': subscriber.client.sim_fragment or None,
            'referral_applied': Referral.objects.filter(referred_subscriber=subscriber).exists(),
            'referral_bonus': subscriber.referral_credit,
            'points': subscriber.perk_points,
            'next_rewards': [{ 'id': 1, 'title': "Simp ticket", 'days_left': 5, 'img': ""}],
            'plans': [{
                'id': subscription.id,
                'pac_code_status': subscription.pac_status,
                'plan_id': plan.id if plan else None,
                'price': plan.price if plan else None,
                'next_bill_amount': subscription.next_bill_amount(),
                'next_bill_date_epoch': self.get_subscription_end_date(cognito_username),
                'potential_cancellation_date_epoch': self.get_subscription_end_date(cognito_username),
                'title': plan.name if plan else None,
                'sim_activation_status': sim.status if sim else 'not-assigned',
                'subscription_status': subscription.status,
                'sim_serial_fragment': sim.serial_number[:-4] if sim else None,
                'self_activated': sim.self_activated if sim else False,
                'can_upgrade': subscription.can_upgrade,
                'can_downgrade': subscription.can_downgrade,
                'can_cancel': subscription.can_cancel,
                'can_cancel_change': subscription.can_cancel_change,
                'latest_plan_change': subscription.plan_changes.order_by('-id').first(),
                'bg': plan.bg if plan else 0,
                'sim_type': sim.sim_type if sim else (subscriber.intended_sim_type if subscriber.intended_sim_type else 'physical'),
                'notifications': {
                    'show_number_transfer': subscription.show_number_transfer,
                    'show_number_porting_progress': subscription.show_number_porting_progress,
                    'show_update_apn': subscription.show_update_apn,
                    'show_set_up_esim': subscription.show_set_up_esim,
                } if subscription else None,
                'plan_changes': [{
                    'status': plan_change.status,
                    'change_type': plan_change.change_type,
                    'id': plan_change.id,
                    'subscriber_status_display': plan_change.subscriber_status_display,
                    'target_plan_id': plan_change.target_plan.id if plan_change.target_plan else None,
                    'target_plan_change_id': plan_change.target_plan_change.id if plan_change.target_plan_change else None,
                } for plan_change in subscription.plan_changes.all()],
                'data': {
                    'left': None if sim.latest_plan.data_is_unlimited else bytes_to_gb(sim.data_limit_bytes_this_month) - data_usage,
                    'total': data_usage,
                    'is_unlimited': sim.latest_plan.data_is_unlimited
                } if sim else None,
                'voice': {
                    'left': None,
                    'total': 'unlimited',
                    'is_unlimited': True
                } if sim else None,
                'sms': {
                    'left': None,
                    'total': 'unlimited',
                    'is_unlimited': True
                } if sim else None,
                "bolt_ons": [
                 {
                    'id': bolt_on_purchase.bolt_on.id,
                    'type': bolt_on_purchase.bolt_on_type,
                    'title': bolt_on_purchase.bolt_on.display_name,
                    'expires': bolt_on_purchase.expires.date(),
                    'status': 'active' if bolt_on_purchase.is_active else 'inactive',
                    'data': {
                      'total': bolt_on_purchase.data_limit_gb,
                      'used': bolt_on_purchase.used_data_gb,
                    } if bolt_on_purchase.bolt_on_type == 'data' else None,
                    'voice': {
                      'total': bolt_on_purchase.voice_limit_minutes,
                      'used': bolt_on_purchase.used_voice_minutes,
                    } if bolt_on_purchase.bolt_on_type == 'voice' else None,
                  }
                    for bolt_on_purchase in subscription.bolt_on_purchases.all()
                ],
                "roaming_bolt_on": roaming_bolt_on if roaming_bolt_on else None,
                "roaming_bolt_on_eu" : roaming_bolt_on_eu if roaming_bolt_on_eu else None,
            }] if subscription else None,
            'first_name': None,
            'last_name': None,
            'address': subscriber.address,
        }

    def get_referral_data(self, cognito_username):
        subscriber = Subscriber.objects.filter(cognito_username=cognito_username, client=self.client).get()
        return {
            'referral_code': subscriber.referral_code,
            'referral_credit': subscriber.referral_credit,
            'referrals': [
                {
                    'id': referral.id,
                    'name': referral.referred_subscriber.name,
                    'date': referral.referral_date,
                    'credit_applied': referral.credit_applied,
                }
                for referral in subscriber.referrals_given.all()
            ],
        }

    def update_address(self, cognito_username, address):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscriber.address = address
        subscriber.save()

    def update_subscriber_details(self, cognito_username, date_of_birth, name):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscriber.date_of_birth = date_of_birth
        subscriber.name = name
        subscriber.save()

    def update_sim_type(self, cognito_username, sim_type):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscriber.intended_sim_type = sim_type
        subscriber.save()
        subscription = subscriber.subscriptions.first()
        if subscription:
            subscription.maybe_activate_esim()

    def get_subscription_end_date(self, _):
        return STRIPE_BILLING_TIME_CONTROL.start_of_next_cycle.timestamp()

    def get_subscription_billing_id(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription:
            return subscription.billing_subscription_id
        return None

    def get_subscription_id(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription:
            return subscription.id
        return None

    def get_subscriber_by_cognito_username(self, cognito_username):
        return Subscriber.objects.filter(cognito_username=cognito_username, client=self.client).get()

    def request_pac_code(self, pac_code, phone_number, desired_date, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription:
            pac_request = PACRequest.objects.create(subscription=subscription, pac_code=pac_code, phone_number=phone_number, desired_date=desired_date)
            if not subscriber.client.provider.is_demo:
                send_slack_message(f'New port-in request for {subscription} on {self.client}: [link in nexus](https://nexus.shaka.tel/admin/core/pacrequest/{pac_request.pk}/change/)')
            send_port_in_requested_email(subscription.subscriber.email, self.client, subscription.pk, phone_number, desired_date)
            if subscription.latest_sim:
                subscription.latest_sim.send_pac_request_if_necessary()
            return True
        return False

    def try_activate_sim(self, sim_digits, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription:
            if subscription.latest_sim is None:
                if subscription.try_activating_sim_by_digits(sim_digits):
                    return True
        return False

    def verify_user(self, cognito_username, code):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        if subscriber.verify_attempts >= 10:
            subscriber.verify_attempts += 1
            subscriber.save()
            return 'Too many attempts'
        if code == subscriber.verification_code:
            subscriber.verification_code = ''
            subscriber.save()
            return 'ok'
        else:
            subscriber.verify_attempts += 1
            subscriber.save()
            return 'Invalid passcode - please check your email again'

    def resend_verification_email(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        send_verification_email(subscriber.email, self.client, subscriber.pk)

    def upgrade_plan(self, cognito_username, plan_id):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription and subscription.can_upgrade:
            upgrade_to_plan(subscription.pk, plan_id)
            return True
        return False

    def downgrade_plan(self, cognito_username, plan_id):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription and subscription.can_downgrade:
            downgrade_to_plan(subscription.pk, plan_id)
            return True
        return False

    def cancel_plan_change(self, cognito_username, plan_change_id):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription and subscription.can_cancel_change:
            cancel_plan_change(plan_change_id)
            return True
        return False

    def cancel_subscription(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription and subscription.can_cancel:
            send_slack_message(f'Cancel subscription {subscription} for {subscriber.email} on {self.client}')
            cancel_subscription(subscription.pk)
            send_cancel_subscription_email(subscription.pk, self.client)
            return True
        return False

    def get_plan_change(self, cognito_username, plan_change_id):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        return subscriber.subscriptions.first().plan_changes.filter(pk=plan_change_id).get()

    def get_plan_changes(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        return subscriber.subscriptions.first().plan_changes.all()

    def get_all_perks(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        for perk in self.client.perks.filter(enabled=True):
            yield {
                'id': perk.id,
                'title': perk.title,
                'type': 'bolt-on' if perk.is_bolt_on else ('discount' if perk.is_discount else 'voucher'),
                'description': perk.description,
                'redeemDetails': perk.redeem_details,
                'image': perk.perk_image or perk.perk_image_link,
                'image_link': perk.perk_image_link,
                'logo_link': perk.perk_logo_link,
                'eligibilityType': perk.eligibility_type,
                'cost': perk.eligibility_threshold,
                'progress': subscriber.progress_towards_perk(perk),
                'isClaimed': subscriber.has_claimed_perk(perk),
                'claimHistory': [
                    {'date': redemption.redeemed_on.strftime('%Y-%m-%dT%H:%M:%SZ'),
                     'paidWithPoints': redemption.points_paid > 0 if redemption.points_paid else False,
                     }
                    for redemption in subscriber.perk_redemptions.filter(perk=perk)
                ],
                'remaining': perk.remaining_quantity,
                'multiplyRedeemable': perk.allow_multiple_redemptions and perk.elective_redemption_cost and perk.elective_redemption_cost > 0,
                'isRedemptionLimit': perk.redemption_limit > 0,
                'enabled': perk.enabled,
                'featured': perk.featured,
                'amount': perk.elective_redemption_cost,
                'availabilityDate': perk.availability_date.strftime('%Y-%m-%dT%H:%M:%SZ') if perk.availability_date else None,
                'details': perk.details if subscriber.has_claimed_perk(perk) else None,
            }

    def get_perk_point_balance(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        return subscriber.perk_points

    def claim_perk(self, cognito_username, perk_id, claim_with_points):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        perk = self.client.perks.get(pk=perk_id)
        if claim_with_points:
            redemption = subscriber.claim_perk_via_points(perk)
        else:
            redemption = subscriber.claim_perk_via_eligibility(perk)
        if redemption:
            if perk.is_voucher:
                send_voucher_details(subscriber.email, self.client, redemption, subscriber.pk)
            if not subscriber.client.provider.is_demo:
                send_slack_message(f'Perk purchased (or attempted) [link in nexus](https://nexus.shaka.tel/admin/core/perkredemption/{redemption.pk}/change/)')
            return {'success': 'Perk claimed'}
        else:
            if perk.remaining_quantity <= 0:
                return {'error': 'Perk not claimed - out of stock'}
            return {'error': 'Perk not claimed - insufficient points or not eligible'}

    def send_esim_instructions(self, cognito_username, email):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscription = subscriber.subscriptions.first()
        if subscription.latest_sim and subscription.latest_sim.is_esim:
            subscription.esim_recipient = email
            subscription.save()

    def get_all_bolt_ons(self):
        return [
            {
                'id': bolt_on.id,
                'title': bolt_on.display_name,
                'price': bolt_on.price,
                'data': bolt_on.data_gb if bolt_on.data_gb else None,
                'voice': bolt_on.voice_minutes if bolt_on.voice_minutes else None,
            }
            for bolt_on in self.client.bolt_ons.enabled().all()
        ]

    def is_close_to_roaming_bolt_on_cutoff(self):
        return (self.client.provider.time_control.end_of_today - timezone.now()).total_seconds() < 3600

    def get_roaming_bolt_ons(self):
        if self.is_close_to_roaming_bolt_on_cutoff():
            return []
        return [
            {
                'id': bolt_on.id,
                'title': bolt_on.display_name,
                'price': bolt_on.price,
                'data': bolt_on.data_gb if bolt_on.data_gb else 0,
                'voice': bolt_on.voice_minutes if bolt_on.voice_minutes else 0,
                'sms': bolt_on.sms if bolt_on.sms else 0,
                'zone': bolt_on.roaming_zone.lower(),
                'valid_until': bolt_on.nearest_upcoming_expiry,
            }
            for bolt_on in self.client.bolt_ons.enabled().roaming().all()
        ]

    def get_purchasable_bolt_on(self, bolt_on_id):
        return self.get_purchasable_bolt_ons([bolt_on_id])[0]

    def get_purchasable_bolt_ons(self, bolt_on_ids):
        return self.client.bolt_ons.enabled().filter(pk__in=bolt_on_ids)

    def hide_notification_dialog(self, cognito_username, dialog_type):
        subscriber = Subscriber.objects.filter(cognito_username=cognito_username, client=self.client).get()
        subscription = subscriber.subscriptions.first()

        if subscription:
            if dialog_type == 'number_transfer':
                subscription.show_number_transfer = False
            elif dialog_type == 'number_porting_progress':
                subscription.show_number_porting_progress = False
            elif dialog_type == 'update_apn':
                subscription.show_update_apn = False
            elif dialog_type == 'set_up_esim':
                subscription.show_set_up_esim = False
            subscription.save()
            return self.get_subscriber_data(cognito_username)
        return None

    def send_password_changed_email(self, cognito_username):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        send_password_changed(subscriber.email, self.client)
        return True


    def update_marketing_subscription(self, cognito_username, send_marketing):
        subscriber = self.get_subscriber_by_cognito_username(cognito_username)
        subscriber.send_marketing = send_marketing
        subscriber.save()

    def get_e_sim_settings(self, cognito_username):
        subscriber = Subscriber.objects.filter(cognito_username=cognito_username, client=self.client).get()
        subscription = subscriber.subscriptions.first()
        if subscription:
            sim = subscription.latest_sim
            if sim and sim.is_esim:
                return {
                    'qr_code': f'data:image/png;base64,{sim.esim_qr_as_base64}',
                    'ios_settings': {
                        'sm_dp': sim.esim_address,
                        'activation_code': sim.esim_code,
                        'app_link': f'https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=${sim.esim_code}'
                    },
                    'android_settings': {
                        'sm_dp_activation': sim.esim_data
                    }
                }
        return {
            'qr_code': '',
            'ios_settings': {
                'sm_dp': '',
                'activation_code': '',
            },
            'android_settings': {
                'sm_dp_activation': ''
            }
        }

    def apply_referral(self, cognito_username, code):
        subscriber = Subscriber.objects.filter(cognito_username=cognito_username, client=self.client).get()

        if subscriber:
            if code:
                referrer = Subscriber.objects.filter(referral_code=code, client=self.client).first()
                if referrer:
                    subscriber.referred_by = referrer
                    subscriber.save()
                    return self.get_subscriber_data(cognito_username)
        return None

    def get_openid_login_data(self):
        if self.client.is_openid_subscriber_auth:
            return generate_oauth_login_details(self.client)
        raise NotImplementedError('OpenID Connect is not enabled for this client')

    def login_via_openid(self, code, state, state_plaintext, code_verifier):
        if self.client.is_openid_subscriber_auth:
            subscriber_details = get_openid_user_params(self.client, code, state, state_plaintext, code_verifier)
            subscriber = self.client.subscribers.filter(cognito_username=subscriber_details['username'])
            email = subscriber_details['email']
            if email is None:
                raise ValueError('No email found in OpenID Connect response')
            if subscriber.count() == 1:
                subscriber = subscriber.get()
                if subscriber.email != email:
                    subscriber.email = email
                name = subscriber_details.get('name')
                if name and subscriber.name != name:
                    subscriber.name = name
                    subscriber.save()
            elif subscriber.count() == 0:
                stripe_client = get_stripe_client(self.billing_secret_key)
                customer = stripe_client.customers.create(params={'email': email})
                subscriber = Subscriber.objects.create(
                    email=email,
                    join_date=timezone.now(),
                    client=self.client,
                    name=subscriber_details.get('name', email),
                    cognito_username=subscriber_details['username'],
                    billing_subscriber_id=customer.id,
                    verification_code='')
                self.perform_additional_new_subscriber_setup(subscriber)
            else:
                raise ValueError('Multiple subscribers found with same username')
            return generate_fresh_auth_tokens(subscriber)

        raise NotImplementedError('OpenID Connect is not enabled for this client')

    def login_via_cognito_sso(self, code):
        if self.client.is_cognito_subscriber_auth:
            tokens = get_tokens_from_cognito_code(self.client, code)
            if 'id_token' not in tokens:
                raise RuntimeError('tokens?', tokens)
            subscriber_details = pyjwt.decode(tokens['id_token'], options={"verify_signature": False})
            subscriber = self.client.subscribers.filter(cognito_username=subscriber_details['cognito:username'])
            email = subscriber_details['email']
            if email is None:
                raise ValueError('No email found in OpenID Connect response')
            if subscriber.count() == 1:
                subscriber = subscriber.get()
                if subscriber.email != email:
                    subscriber.email = email
                name = subscriber_details.get('name')
                if not name:
                    name = f'{subscriber_details.get("given_name")} {subscriber_details.get("family_name")}'.strip()
                if name and subscriber.name != name:
                    subscriber.name = name
                    subscriber.save()
            elif subscriber.count() == 0:
                stripe_client = get_stripe_client(self.billing_secret_key)
                customer = stripe_client.customers.create(params={'email': email})
                extra_args = {}
                if settings.FORCE_ESIM:
                    extra_args['intended_sim_type'] = 'esim'
                subscriber = Subscriber.objects.create(
                    email=email,
                    join_date=timezone.now(),
                    client=self.client,
                    name=subscriber_details.get('name', email),
                    cognito_username=subscriber_details['cognito:username'],
                    billing_subscriber_id=customer.id,
                    verification_code='',
                    **extra_args)
                self.perform_additional_new_subscriber_setup(subscriber)
            else:
                raise ValueError('Multiple subscribers found with same username')
            return tokens

        raise NotImplementedError('Cognito is not enabled for this client')
