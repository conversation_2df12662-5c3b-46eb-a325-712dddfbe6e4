# clientapp/admin.py
from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.core.exceptions import PermissionDenied
from . import models
from .billing import get_month_usage_for_subscription
from .emails import send_pending_email


class ProviderAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)
    readonly_fields = ('name',)

class PaymentIntegrationAdmin(admin.ModelAdmin):
    list_display = ['client', 'gateway']
    list_filter = ['gateway']

class PaymentIntegrationInline(admin.StackedInline):
    model = models.PaymentIntegration

class ClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'auth_app_domain_prefix', 'authorisation_url')
    list_filter = ('name',)
    search_fields = ('name', 'auth_app_domain_prefix')
    inlines = [PaymentIntegrationInline]

class DashboardUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'client', 'notes', 'email')
    search_fields = ('username',)
    list_filter = ('client',)

class FontAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

class ClientBrandingAdmin(admin.ModelAdmin):
    list_display = ('client', 'headings_font', 'paragraph_font', 'primary_color', 'secondary_color', 'accent_color', 'text_color', 'link_color')
    list_filter = ('headings_font', 'paragraph_font', 'primary_color', 'secondary_color', 'accent_color', 'text_color', 'link_color')

class SimPlanAssignmentInline(admin.TabularInline):
    model = models.SimPlanAssignment
    extra = 1

class SimSubscriptionAssignmentInline(admin.TabularInline):
    model = models.SimSubscriptionAssignment
    extra = 1
    raw_id_fields = ('subscription',)

class SubscriptionInline(admin.TabularInline):
    model = models.Subscription
    extra = 1

class SimPhoneNumberInline(admin.TabularInline):
    model = models.NumberAssignment
    extra = 1

class PlanAdmin(admin.ModelAdmin):
    list_display = ('name', 'client', 'provider', 'price', 'status', 'created',
                    'implementation_datetime', 'cost_override', 'voice_component_offering',
                    'custom_voice_limit', 'sms_component_offering', 'custom_sms_limit',
                    'data_component_offering', 'custom_data_limit', 'is_bundled')
    search_fields = ('name', 'client__name', 'provider__name', 'plan_key')
    list_filter = ('status', 'is_bundled', 'provider', 'client')


class PlanComponentAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'provider', 'dimension', 'bundle_only', 'allow_custom_limit', 'max_limit')
    search_fields = ('description', 'provider__name')
    list_filter = ('dimension', 'bundle_only', 'allow_custom_limit', 'provider')


class PlanComponentOfferingAdmin(admin.ModelAdmin):
    list_display = ('client', 'plan_component', 'cost_override', 'price_override', 'available_for_new_plans')
    search_fields = ('client__name', 'plan_component__description')
    list_filter = ('available_for_new_plans', 'plan_component__dimension', 'client', 'client__provider')


class BundledPlanOfferingAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'client', 'voice', 'sms', 'data', 'cost_override', 'price_override', 'is_available')
    search_fields = ('client__name', 'voice__plan_component__description', 'sms__plan_component__description', 'data__plan_component__description')
    list_filter = ('is_available',)



class SimAdmin(admin.ModelAdmin):
    list_display = ('serial_number', 'status', 'display_subscriber_email_link', 'display_latest_number_link', 'display_latest_plan_link')
    list_filter = ('status', 'esim_available_to', 'dispatched_by')
    search_fields = ('serial_number', 'subscriptions__subscriber__email', 'number_assignments__phone_number')
    inlines = [SimPlanAssignmentInline, SimSubscriptionAssignmentInline, SimPhoneNumberInline]

    def display_latest_number_link(self, obj):
        latest_number = obj.latest_number
        if latest_number:
            link = reverse('admin:core_numberassignment_change', args=[latest_number.id])
            return format_html('<a href="{}">{}</a>', link, latest_number.phone_number)
        return None
    display_latest_number_link.short_description = 'Latest number'

    def display_subscriber_email_link(self, obj):
        subscriber = obj.latest_subscriber
        if subscriber:
            link = reverse('admin:core_subscriber_change', args=[subscriber.id])
            return format_html('<a href="{}">{}</a>', link, subscriber.email)
        return None
    display_subscriber_email_link.short_description = 'Latest email'

    def display_latest_plan_link(self, obj):
        latest_plan = obj.latest_plan
        if latest_plan:
            link = reverse('admin:core_plan_change', args=[latest_plan.id])
            return format_html('<a href="{}">{}</a>', link, latest_plan)
        return None
    display_latest_plan_link.short_description = 'Latest plan'


class SubscriberAdmin(admin.ModelAdmin):
    list_display = ('email', 'join_date', 'display_client_link', 'display_subscription_links')
    search_fields = ('email', 'client__name')
    inlines = [SubscriptionInline]

    def display_client_link(self, obj):
        client = obj.client
        link = reverse('admin:core_client_change', args=[client.id])
        return format_html('<a href="{}">{}</a>', link, client.name)
    display_client_link.short_description = 'Client'

    def display_subscription_links(self, obj):
        subscriptions = obj.subscriptions.all()
        links = [reverse('admin:core_subscription_change', args=[subscription.id]) for subscription in subscriptions]
        return format_html(', '.join([f'<a href="{link}">{subscription}</a>' for link, subscription in zip(links, subscriptions)]))
    display_subscription_links.short_description = 'Subscriptions'


def humanize_bytes(num_bytes):
    if num_bytes:
        for unit in ["B", "KiB", "MiB", "GiB", "TiB"]:
            if num_bytes < 1024.0:
                return f"{num_bytes:3.1f} {unit}"
            num_bytes /= 1024.0
        return f"{num_bytes:.1f} PiB"
    return '0 B'

class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ('subscriber', 'status', 'start_date', 'display_sims_links', 'display_latest_sim_link', 'display_latest_number_link', 'display_latest_plan_link', 'this_month_data_limit', 'this_month_data_usage', 'last_month_data_usage', 'sim_type')
    list_filter = ('status', 'subscriber__client')
    search_fields = ('subscriber__email', 'sims__serial_number')
    inlines = [SimSubscriptionAssignmentInline]
    fields = ['start_date', 'subscriber', 'sim_type', 'display_latest_sim_link', 'display_latest_number_link', 'display_latest_plan_link', 'this_month_data_limit', 'this_month_data_usage', 'billing_subscription_id', 'status', 'intended_plan']
    readonly_fields = ['sim_type', 'display_latest_sim_link', 'display_latest_number_link', 'display_latest_plan_link', 'this_month_data_limit', 'this_month_data_usage', 'last_month_data_usage']
    raw_id_fields = ['subscriber']

    def display_sims_links(self, obj):
        sims = obj.sims.all()
        links = [reverse('admin:core_sim_change', args=[sim.id]) for sim in sims]
        return format_html(', '.join([f'<a href="{link}">{sim.serial_number}</a>' for link, sim in zip(links, sims)]))
    display_sims_links.short_description = 'SIMs'

    def display_latest_sim_link(self, obj):
        latest_sim = obj.latest_sim
        if latest_sim:
            link = reverse('admin:core_sim_change', args=[latest_sim.id])
            return format_html(f'<a href="{link}">{latest_sim.serial_number}</a>')
        return None
    display_latest_sim_link.short_description = 'Latest SIM'

    def display_latest_number_link(self, obj):
        latest_number = obj.latest_number
        if latest_number:
            link = reverse('admin:core_numberassignment_change', args=[latest_number.id])
            return format_html(f'<a href="{link}">{latest_number.phone_number}</a>')
        return None
    display_latest_number_link.short_description = 'Latest number'

    def display_latest_plan_link(self, obj):
        latest_plan = obj.latest_plan
        if latest_plan:
            link = reverse('admin:core_plan_change', args=[latest_plan.id])
            return format_html(f'<a href="{link}">{latest_plan}</a>')
        return None
    display_latest_plan_link.short_description = 'Latest plan'

    def this_month_data_limit(self, obj):
        if obj.latest_sim:
            return obj.latest_sim.data_limit_gb_this_month
        return ''

    def this_month_data_usage(self, obj):
        if obj.latest_msisdn:
            start, end = obj.time_control.current_month_datetimes
            return humanize_bytes(get_month_usage_for_subscription(obj, start, end, 'data'))
        return ''

    def last_month_data_usage(self, obj):
        if obj.latest_msisdn:
            start, end = obj.time_control.last_month_datetimes
            return humanize_bytes(get_month_usage_for_subscription(obj, start, end, 'data'))
        return ''

    def sim_type(self, obj):
        if obj.latest_sim:
            return obj.latest_sim.sim_type
        return ''


class SubscriptionPaymentAdmin(admin.ModelAdmin):
    list_display = ('subscription', 'amount', 'currency', 'date')
    raw_id_fields = ('subscription',)


class NumberAssignmentAdmin(admin.ModelAdmin):
    raw_id_fields = ('sim',)
    list_display = ('phone_number', 'start_date', 'end_date', 'sim')
    search_fields = ('phone_number', 'sim__serial_number')

class WebhookLogAdmin(admin.ModelAdmin):
    readonly_fields = ('date', 'webhook_source', 'payload', 'supplemental_data')
    list_display = ('date', 'webhook_source')
    list_filter = ('webhook_source',)
    search_fields = ('payload', 'supplemental_data')


class WebhookActionLogAdmin(admin.ModelAdmin):
    readonly_fields = ('log', 'webhook_log', 'payload', 'client', 'date')
    list_display = ('log', 'status', 'webhook_log', 'client', 'get_event_type')
    list_filter = ('webhook_log__webhook_source', 'status', 'webhook_log__client', 'webhook_log__date')
    search_fields = ('log', 'webhook_log__payload', 'webhook_log__supplemental_data')

    def get_event_type(self, obj):
        try:
            return obj.webhook_log.payload['header']['eventType']
        except KeyError:
            try:
                return obj.webhook_log.payload['type']
            except KeyError:
                return "Type not found"

class PACRequestAdmin(admin.ModelAdmin):
    raw_id_fields = ('subscription',)
    list_display = ('subscription', 'pac_code', 'msisdn', 'display_subscription_link', 'desired_date')

    def display_subscription_link(self, obj):
        subscription = obj.subscription
        link = reverse('admin:core_subscription_change', args=[subscription.id])
        return format_html(f'<a href="{link}">{subscription}</a>')
    display_subscription_link.short_description = 'Subscription'

class PortabilityEventAdmin(admin.ModelAdmin):
    list_display = ('sim', 'status', 'msisdn', 'display_subscription_link')
    list_filter = ('status', )
    search_fields = ('sim__id', 'msisdn', 'sim__serial_number')
    ordering = ('-id',)

    def display_subscription_link(self, obj):
        subscription = obj.sim.latest_subscription
        if subscription:
            link = reverse('admin:core_subscription_change', args=[subscription.id])
            return format_html(f'<a href="{link}">{subscription}</a>')
        return str(obj.pk)
    display_subscription_link.short_description = 'Subscription'

class EmailConfigurationAdmin(admin.ModelAdmin):
    list_display = ('client', 'provider', 'from_address', 'list_email_types')
    list_filter = ('provider', 'client')
    search_fields = ('client__name', 'from_address')
    filter_horizontal = ('email_types',)
    fieldsets = (
        (None, {
            'fields': ('client', 'provider', 'from_address', 'client_name', 'client_mobile_name', 'client_footer', 'support_email', 'support_link', 'help_esim_install_link', 'help_keep_number_link', 'help_data_issue', 'plan_color_hex', 'plan_text_color_hex')
        }),
        ('Advanced options', {
            'classes': ('collapse',),
            'fields': ('identity', 'credential', 'endpoint', 'email_types', 'debug_bcc'),
        }),
    )

    def list_email_types(self, obj):
        return ", ".join([email_type.name for email_type in obj.email_types.all()])
    list_email_types.short_description = 'Email Types'


class EmailTypeAdmin(admin.ModelAdmin):
    list_display = ('name',)
    readonly_fields = ('name',)

def send_all_pending_emails(_, request, queryset):
    if not request.user.is_superuser:
        raise PermissionDenied
    pending_emails = queryset.filter(status=models.SystemEmail.EmailStatus.PENDING)
    for email in pending_emails:
        send_pending_email(email)  # Assuming send_pending_email is already defined and imported

send_all_pending_emails.short_description = "Send all pending emails"


class SystemEmailAdmin(admin.ModelAdmin):
    list_display = ('recipient', 'email_type', 'client', 'status', 'created', 'sent_at')
    list_filter = ('status', 'created', 'sent_at', 'client', 'email_type')
    search_fields = ('recipient', 'client__name', 'email_type__name')
    date_hierarchy = 'created'
    readonly_fields = ('created',)
    actions = [send_all_pending_emails]


class SMSMessageAdmin(admin.ModelAdmin):
    list_display = ('message', 'status', 'client', 'send_on', 'initiator', 'created', 'campaign')
    list_filter = ('status', 'created')
    search_fields = ('message', 'client__name', 'initiator__user__username')


class SMSConfigurationAdmin(admin.ModelAdmin):
    list_display = ('client', 'provider')
    list_filter = ('provider', 'client')
    search_fields = ('client__name', )
    fieldsets = (
        (None, {
            'fields': ('client', 'provider', 'allow_out_of_hours_sending')
        }),
        ('Advanced options', {
            'classes': ('collapse',),
            'fields': ('identity', 'credential', 'endpoint', 'sender', 'delivery_report_url'),
        }),
    )



class MarketingEligibilityAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'eligibility_type', 'active_since', 'active_duration_months')
    list_filter = ('eligibility_type',)
    search_fields = ('eligibility_type',)
    ordering = ('-active_since',)


class PlanDiscountInline(admin.TabularInline):
    model = models.PlanDiscount
    extra = 0

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        if obj:
            # Filter queryset by the campaign's client
            client = obj.client
            formset.form.base_fields['plan'].queryset = models.Plan.objects.filter(client=client)
        return formset

class CampaignAdmin(admin.ModelAdmin):
    list_display = ('title', 'client', 'campaign_type', 'start_date', 'end_date', 'status')
    list_filter = ('status', 'campaign_type', 'start_date', 'end_date')
    search_fields = ('title', 'client__name')
    ordering = ('-start_date',)
    inlines = [PlanDiscountInline]

class PlanDiscountAdmin(admin.ModelAdmin):
    list_display = ('campaign', 'discount_perk', 'plan', 'discount_type', 'discount_percentage', 'discount_amount', 'discount_duration_months')
    list_filter = ('discount_type', 'campaign', 'plan')
    search_fields = ('campaign__title', 'plan__name')

class DiscountApplicationAdmin(admin.ModelAdmin):
    list_display = ('plan_discount', 'subscription', 'applied_at')
    raw_id_fields = ('subscription','plan_discount')
    date_hierarchy = 'applied_at'

class PlanChangeAdmin(admin.ModelAdmin):
    list_display = ('subscription', 'change_type', 'target_plan', 'execution_start_time', 'status', 'execution_id')
    list_filter = ('change_type', 'status', 'execution_start_time', 'target_plan')
    search_fields = ('subscription__user__username', 'execution_id')
    raw_id_fields = ('subscription',)

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('subscription', 'target_plan')
        return queryset


class PerkModelAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'client', 'eligibility_type', 'eligibility_threshold',
        'allow_multiple_redemptions', 'electively_redeemable',
        'elective_redemption_cost', 'availability_date', 'redemption_limit',
        'enabled', 'featured'
    )
    list_filter = (
        'client', 'eligibility_type', 'allow_multiple_redemptions',
        'electively_redeemable', 'enabled'
    )
    search_fields = ('name', 'description')
    ordering = ('client', 'name')
    readonly_fields = ('perk_image',)
    fieldsets = (
        (None, {
            'fields': ('name', 'client', 'description', 'cost_base', 'perk_image', 'perk_image_link', 'perk_logo_link')
        }),
        ('Eligibility', {
            'fields': ('eligibility_type', 'eligibility_threshold', 'target_group')
        }),
        ('Redemption', {
            'fields': ('allow_multiple_redemptions', 'electively_redeemable', 'elective_redemption_cost')
        }),
        ('Availability', {
            'fields': ('availability_date', 'redemption_limit', 'enabled', 'featured')
        }),
    )


class DiscountPerkModelAdmin(PerkModelAdmin):
    inlines = [PlanDiscountInline]


class VoucherPerkModelAdmin(PerkModelAdmin):
    pass

class BoltOnPerkModelAdmin(PerkModelAdmin):
    pass

class PerkRedemptionAdmin(admin.ModelAdmin):
    list_display = ('perk', 'display_client_link', 'subscriber', 'redeemed_on', 'points_paid', 'fulfilment_status')
    list_filter = ('perk', 'perk__client', 'fulfilment_status')
    search_fields = ('perk__name', 'subscriber__email', 'subscriber__client__name')
    date_hierarchy = 'redeemed_on'

    def display_client_link(self, obj):
        client = obj.perk.client
        link = reverse('admin:core_client_change', args=[client.id])
        return format_html(f'<a href="{link}">{client}</a>')
    display_client_link.short_description = 'Client'

class PerkPointAllocationAdmin(admin.ModelAdmin):
    list_display = ('subscription', 'amount', 'date')
    raw_id_fields = ('subscription',)
    search_fields = ('subscription__subscriber__email',)

class DemoUsageAdmin(admin.ModelAdmin):
    list_display = ('client', 'sim', 'dimension', 'value')
    list_filter = ('client', 'dimension')
    search_fields = ('client__name', 'sim__serial_number')


class BoltOnAdmin(admin.ModelAdmin):
    list_display = ('name', 'provider', 'provider_code', 'default_price', 'default_cost', 'bolt_on_type', 'duration_type', 'billing_calendar_sync')
    list_filter = ('provider', 'bolt_on_type', 'duration_type', 'billing_calendar_sync')
    search_fields = ('name', 'provider__name', 'provider_code')


class BoltOnOfferingAdmin(admin.ModelAdmin):
    list_display = ('bolt_on', 'client', 'cost_override', 'price_override', 'available_for_new_selection')
    list_filter = ('client', 'available_for_new_selection')
    search_fields = ('bolt_on__name', 'client__name')


class ClientBoltOnAdmin(admin.ModelAdmin):
    list_display = ('offering', 'client', 'price', 'name_override', 'status', 'version_number', 'implementation_datetime')
    list_filter = ('client', 'status')
    search_fields = ('offering__bolt_on__name', 'client__name', 'name_override')


class BoltOnPurchaseAdmin(admin.ModelAdmin):
    list_display = ('bolt_on', 'subscription', 'purchase_datetime', 'price', 'redeemed_via_perk')
    raw_id_fields = ('subscription',)
    list_filter = ('bolt_on__client',)
    date_hierarchy = 'purchase_datetime'
    search_fields = ('bolt_on__name', 'subscription__subscriber__email')


class EuRoamingTrackingAdmin(admin.ModelAdmin):
    raw_id_fields = ('subscription',)
    list_display = ('year', 'month', 'subscription', 'roaming_days')
    list_filter = ('year', 'month')

admin.site.register(models.Provider, ProviderAdmin)
admin.site.register(models.Plan, PlanAdmin)
admin.site.register(models.PlanComponent, PlanComponentAdmin)
admin.site.register(models.PlanComponentOffering, PlanComponentOfferingAdmin)
admin.site.register(models.BundledPlanOffering, BundledPlanOfferingAdmin)
admin.site.register(models.Sim, SimAdmin)
admin.site.register(models.Subscriber, SubscriberAdmin)
admin.site.register(models.Subscription, SubscriptionAdmin)
admin.site.register(models.NumberAssignment, NumberAssignmentAdmin)
admin.site.register(models.PaymentIntegration, PaymentIntegrationAdmin)
admin.site.register(models.Client, ClientAdmin)
admin.site.register(models.DashboardUser, DashboardUserAdmin)
admin.site.register(models.Font, FontAdmin)
admin.site.register(models.SubscriptionPayment, SubscriptionPaymentAdmin)
admin.site.register(models.ClientBranding, ClientBrandingAdmin)
admin.site.register(models.WebhookLog, WebhookLogAdmin)
admin.site.register(models.WebhookActionLog, WebhookActionLogAdmin)
admin.site.register(models.PACRequest, PACRequestAdmin)
admin.site.register(models.PortabilityEvent, PortabilityEventAdmin)
admin.site.register(models.EmailConfiguration, EmailConfigurationAdmin)
admin.site.register(models.EmailType, EmailTypeAdmin)
admin.site.register(models.SystemEmail, SystemEmailAdmin)
admin.site.register(models.SMSMessage, SMSMessageAdmin)
admin.site.register(models.SMSConfiguration, SMSConfigurationAdmin)
admin.site.register(models.MarketingEligibility, MarketingEligibilityAdmin)
admin.site.register(models.Campaign, CampaignAdmin)
admin.site.register(models.PlanDiscount, PlanDiscountAdmin)
admin.site.register(models.DiscountApplication, DiscountApplicationAdmin)
admin.site.register(models.PlanChange, PlanChangeAdmin)
admin.site.register(models.DiscountPerk, DiscountPerkModelAdmin)
admin.site.register(models.VoucherPerk, VoucherPerkModelAdmin)
admin.site.register(models.BoltOnPerk, BoltOnPerkModelAdmin)
admin.site.register(models.PerkRedemption, PerkRedemptionAdmin)
admin.site.register(models.DemoUsage, DemoUsageAdmin)
admin.site.register(models.BoltOn, BoltOnAdmin)
admin.site.register(models.BoltOnOffering, BoltOnOfferingAdmin)
admin.site.register(models.ClientBoltOn, ClientBoltOnAdmin)
admin.site.register(models.BoltOnPurchase, BoltOnPurchaseAdmin)
admin.site.register(models.PerkPointAllocation, PerkPointAllocationAdmin)
admin.site.register(models.EuRoamingTracking, EuRoamingTrackingAdmin)

class ReferralAdmin(admin.ModelAdmin):
    list_display = ('referral_code', 'referrer', 'referred_subscriber', 'referral_date', 'credit_applied')
    search_fields = ('referral_code', 'referrer__email', 'referred_subscriber__email')

admin.site.register(models.Referral, ReferralAdmin)
