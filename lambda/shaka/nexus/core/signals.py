from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import SMSMessage, SimSubscriptionAssignment, NumberAssignment
from .sms import send_any_sms
from .emails import send_physical_delivery_email, send_esim_activation_email, send_self_sim_activation_email

@receiver(post_save, sender=SMSMessage)
def sms_message_created(sender, instance, created, **kwargs):  # pylint: disable=unused-argument
    if created:
        send_any_sms(instance.client)

@receiver(post_save, sender=SimSubscriptionAssignment)
def sim_subscription_assignment_saved(sender, instance, created, **kwargs):  # pylint: disable=unused-argument
    sim = instance.sim
    subscription = instance.subscription
    client = sim.client
    if created and client and subscription and sim.latest_number:
        if sim.is_esim:
            send_esim_activation_email(subscription.esim_recipient or subscription.subscriber.email, client, subscription.pk)
        else:
            if sim.self_activated:
                send_self_sim_activation_email(subscription.subscriber.email, client, subscription.pk)
            else:
                send_physical_delivery_email(subscription.pk, client)

@receiver(post_save, sender=NumberAssignment)
def sim_number_assignment_saved(sender, instance, created, **kwargs):  # pylint: disable=unused-argument
    sim = instance.sim
    subscription = sim.latest_subscription
    client = sim.client
    if created and client and subscription and sim.latest_number:
        if sim.is_esim:
            send_esim_activation_email(subscription.esim_recipient or subscription.subscriber.email, client, subscription.pk)
        else:
            send_physical_delivery_email(subscription.pk, client)
