import functools
from typing import TypeVar

from django import forms

from core.provider_specific_cdr import events
from core.topic import topic

T_Event = TypeVar("T_Event")
T_ModelInstance = TypeVar("T_ModelInstance")


def _model_instance_to_event(instance: T_ModelInstance, event_type: type[T_Event]) -> T_Event:
    instance_dict = forms.model_to_dict(instance)
    return topic.converter.structure(instance_dict, event_type)


gamma_data_model_instance_to_event = functools.partial(_model_instance_to_event, event_type=events.GammaDataCDREvent)
gamma_voice_model_instance_to_event = functools.partial(_model_instance_to_event, event_type=events.GammaVoiceCDREvent)
gamma_sms_model_instance_to_event = functools.partial(_model_instance_to_event, event_type=events.GammaSMSCDREvent)
