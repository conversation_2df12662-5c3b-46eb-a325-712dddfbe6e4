import { useState } from 'react';
import ProgressLinks from 'src/components/common/ProgressLinks';
import { RoamingBoltOn, BoltOnCountry } from 'src/types/boltons';
import { getTimeDifferenceFromNow } from 'src/helpers/date';
import Terms from '../Terms';
import { currencyRounded } from 'src/helpers';
import CountryIcon from 'src/components/common/CountryIcon';
import * as countriesIcons from 'country-flag-icons/react/3x2';

export default function BoltOnConfirmation({
  onNextClick,
  onBackClick,
  selectedBoltOn = {} as RoamingBoltOn,
  selectedCountry,
  navPosition
}: {
  onNextClick: (isNewPaymentMethod: boolean) => void;
  onBackClick: () => void;
  selectedCountry?: BoltOnCountry;
  selectedBoltOn?: RoamingBoltOn;
  navPosition: 'fixed' | 'absolute';
}) {
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);

  const handleBoltOnConfirmation = () => {
    onNextClick(false);
  };

  return (
    <div className="pb-20">
      <div className="bg-white rounded-xl px-6 py-4">
        <div className="flex justify-between mb-4">
          <h2 className="text-xl font-semibold w-1/2">
            {selectedBoltOn.title}
          </h2>
          <div className="flex gap-2 items-center">
            <span className="text-base font-semibold text-[#BABABA]">
              {selectedCountry?.label}
            </span>
            <CountryIcon
              Icon={
                countriesIcons[
                  selectedCountry?.value as keyof typeof countriesIcons
                ]
              }
              title={selectedCountry?.label || ''}
              small
            />
          </div>
        </div>
        <div className="flex justify-between">
          <p>
            Valid for the next{' '}
            <span className="font-bold">
              {getTimeDifferenceFromNow(selectedBoltOn.valid_until)}
            </span>
          </p>
          <span className="text-primary text-lg font-semibold">
            {currencyRounded(selectedBoltOn.price)}
          </span>
        </div>
      </div>
      <div className="mt-14 space-y-6">
        <div className="flex justify-between text-xs">
          <span className="font-semibold">SIM card</span>
          <span className="text-gray-500">Main plan</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Activation date</span>
          <span className="text-gray-500">Immediate activation</span>
        </div>
      </div>
      <ProgressLinks
        onNextClick={handleBoltOnConfirmation}
        onBackClick={onBackClick}
        absolute={navPosition === 'absolute'}
      />
      <Terms
        isOpen={isTermsModalOpen}
        onClose={() => setIsTermsModalOpen(false)}
      />
    </div>
  );
}
