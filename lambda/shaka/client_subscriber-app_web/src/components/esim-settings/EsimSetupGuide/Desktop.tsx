import { PhoneCameraIcon } from 'src/assets/icons/PhoneCamera';
import IosSecondStep from './IosSecondStep';
import QrCode from 'src/components/common/QrCode';
import WhiteBlock from 'src/components/common/WhiteBlock';
import InternetWarning from './InternetWarning';

export default function Desktop({ qr_code }: { qr_code: string }) {
  return (
    <div className="space-y-5">
      <WhiteBlock title="Install your eSIM" label="step 1">
        <InternetWarning />
        <div className="flex flex-col justify-center gap-4 text-sm mb-5">
          <div className="flex gap-2 items-center">
            <PhoneCameraIcon />
            <span className="font-semibold">
              Scan this QR code with your phone
            </span>
          </div>
          <span>
            Then tap “Add eSIM” and follow the prompts on-screen prompts to
            install
          </span>
        </div>
        <QrCode qr_code={qr_code} />
      </WhiteBlock>
      <IosSecondStep label="iphone only" labelColor="#E03E8C" />
    </div>
  );
}
