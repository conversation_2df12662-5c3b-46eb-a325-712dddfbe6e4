import { Link, LinkProps } from "@tanstack/react-router";
import clsx from "clsx";
import { PropsWithChildren } from "react";

export const subscriptionActionLinkStyles =
  "flex bg-white px-2 rounded-[100%] text-center text-xs font-bold size-[76px] items-center justify-center hover:bg-white/50";

interface Props {
  disabled?: boolean;
}

const SubscriptionActionLink = ({
  children,
  disabled,
  ...props
}: PropsWithChildren<Props> & LinkProps) => (
  <Link
    className={clsx(
      subscriptionActionLinkStyles,
      disabled && "pointer-events-none opacity-50"
    )}
    {...props}
  >
    {children}
  </Link>
);

export default SubscriptionActionLink;
