import { useNavigate, useRouter } from '@tanstack/react-router';
import clsx from 'clsx';
import { MouseEventHandler } from 'react';
import { CloseIcon, LeftArrowIcon } from 'src/assets/icons';
import { ROUTES } from 'src/config/routes';
import { twMerge } from 'tailwind-merge';

const BackButton = ({
  label,
  to,
  size = 'small',
  onClick,
  iconType = 'default',
  colorMimic
}: {
  label?: string;
  to?: string;
  size?: 'small' | 'medium' | 'large';
  onClick?: MouseEventHandler<HTMLButtonElement>;
  iconType?: 'default' | 'cross' | 'back';
  colorMimic?: boolean;
}) => {
  const navigate = useNavigate();
  const { history } = useRouter();

  const onBack = () => {
    const length = window.history.length;

    if (to) {
      navigate({ to });
      return;
    }
    if (length === 1) {
      navigate({ to: ROUTES.Dashboard });
    }

    history.back();
  };

  return (
    <button
      type="button"
      onClick={onClick || onBack}
      className={twMerge(
        'flex gap-4 items-center z-[1]',
        size === 'small' ? 'absolute top-4 md:top-8 left-5' : 'w-12 h-12'
      )}
    >
      <span
        className={clsx(
          'back-button block',
          size,
          colorMimic && 'backdrop-invert-[0.7]'
        )}
      >
        {(iconType === 'default' || iconType === 'back') && (
          <LeftArrowIcon className="w-5 h-5" />
        )}
        {iconType === 'cross' && <CloseIcon className="size-6 -m-0.5" />}
      </span>
      <span className="font-bold text-lg">{label}</span>
    </button>
  );
};

export default BackButton;
