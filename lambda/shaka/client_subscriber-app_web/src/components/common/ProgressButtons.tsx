import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import Button from "./Button";
import { RightArrowIcon } from "src/assets/icons";
import BackButton from "./BackButton";

interface Props {
  hideBackButton?: boolean;
  hideNextButton?: boolean;
  disabledNext?: boolean;
  onBack?: MouseEventHandler<HTMLButtonElement>;
  onNext?: MouseEventHandler<HTMLButtonElement>;
}

const ProgressButtons = ({
  hideBackButton,
  hideNextButton,
  disabledNext,
  onBack,
  onNext,
}: Props) => {
  return (
    <div className="flex justify-between items-end">
      {!hideBackButton ? (
        <div className="pointer-events-auto">
          <BackButton onClick={onBack} size="medium" />
        </div>
      ) : (
        <div />
      )}
      {!hideNextButton && (
        <div className="pointer-events-auto">
          <Button
            type="submit"
            color="secondary"
            rightIcon={<RightArrowIcon />}
            disabled={disabledNext}
            onClick={onNext}
            narrow
            fullWidth
            transparentBg
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProgressButtons;
