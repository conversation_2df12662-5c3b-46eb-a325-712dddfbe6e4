@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;


:root {
  font-family: 'Poppins', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --primary-color: #347DA8;
  --secondary-color: #FFFFFF;
  --gray-color: #1a1a1a;
  --danger-color: #ff6969;
  --font-color: #000000;
  --secondary-font-color: #FFFFFF;
  --input-color: white;
  --bg-color: #F3F3F3;
  --sign-up-green: #1EC25F;
  --sign-up-red: #E03E8C;
}

body {
  margin: 0;
  min-width: 320px;
  background: var(--bg-color);
}

.text-primary {
  color: var(--primary-color);
}

.text-green {
  color: var(--sign-up-green);
}

.text-error {
  color: var(--danger-color);
}

.bg-primary {
  background: var(--primary-color);
}

.bg-general {
  background: var(--bg-color);
}

.bg-green {
  background: var(--sign-up-green);
}

.slideContainer {
  position: absolute;
  height: 100%;
  top: 50%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: 50% 50%;
}

.slideContainer>div {
  box-shadow: 0px 2px 5px 0px rgba(115, 115, 115, 0.2);
}

.slideCard {
  position: relative;
  min-width: 30%;
  width: 100vw;
  height: 100%;
  background: #cb8fff;
  font-size: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: 50% 50%;
  border-radius: 24px;
  box-shadow: 0 0 15px -10px;
}

.plan-card {
  background-size: 100% 100%;
  clip-path: polygon(34.83% 3.557%, 34.83% 3.557%, 34.756% 2.996%, 34.636% 2.462%, 34.475% 1.963%, 34.275% 1.507%, 34.04% 1.101%, 33.774% 0.752%, 33.479% 0.468%, 33.16% 0.256%, 32.819% 0.123%, 32.46% 0.077%, 7.449% 0.077%, 7.449% 0.077%, 6.253% 0.229%, 5.119% 0.67%, 4.061% 1.374%, 3.095% 2.319%, 2.236% 3.48%, 1.5% 4.834%, 0.9% 6.357%, 0.453% 8.024%, 0.174% 9.811%, 0.077% 11.696%, 0.077% 88.381%, 0.077% 88.381%, 0.174% 90.266%, 0.453% 92.054%, 0.9% 93.721%, 1.5% 95.243%, 2.236% 96.597%, 3.095% 97.758%, 4.061% 98.703%, 5.119% 99.408%, 6.253% 99.848%, 7.449% 100%, 92.629% 100%, 92.629% 100%, 93.824% 99.848%, 94.959% 99.408%, 96.016% 98.703%, 96.982% 97.758%, 97.841% 96.597%, 98.578% 95.243%, 99.177% 93.721%, 99.624% 92.054%, 99.904% 90.266%, 100% 88.381%, 100% 11.696%, 100% 11.696%, 99.904% 9.811%, 99.624% 8.024%, 99.177% 6.357%, 98.578% 4.834%, 97.841% 3.48%, 96.982% 2.319%, 96.016% 1.374%, 94.959% 0.67%, 93.824% 0.229%, 92.629% 0.077%, 69.002% 0.077%, 69.002% 0.077%, 68.643% 0.123%, 68.302% 0.256%, 67.982% 0.468%, 67.688% 0.752%, 67.421% 1.101%, 67.187% 1.507%, 66.987% 1.963%, 66.825% 2.462%, 66.706% 2.996%, 66.631% 3.557%, 66.631% 3.557%, 66.138% 7.154%, 65.334% 10.542%, 64.245% 13.68%, 62.894% 16.528%, 61.309% 19.047%, 59.513% 21.198%, 57.532% 22.939%, 55.391% 24.233%, 53.115% 25.038%, 50.731% 25.315%, 50.731% 25.315%, 48.346% 25.038%, 46.071% 24.233%, 43.93% 22.939%, 41.949% 21.198%, 40.153% 19.047%, 38.567% 16.528%, 37.216% 13.68%, 36.127% 10.542%, 35.323% 7.154%, 34.83% 3.557%);
}

.plan-card--cancelled {
  background: #8b8b8b;
}

.plan-card-content {
  flex-grow: 1;
  margin-top: 3.5rem;
  text-align: center;
}

@media (max-width: 374px) {
  .plan-card-content {
    margin-top: 1rem;
  }
}

@media (max-width: 420px) {
  .plan-card-content {
    margin-top: 2.5rem;
  }
}

.plan-card-content.plain {
  width: 100%;
  margin-top: 1.5rem;
  text-align: left;
  text-transform: capitalize;
}

.plan-card-detailed {
  border-radius: 28px;
  background-size: 100% 100%;
  color: var(--secondary-font-color);
}

@media screen and (min-width: 425px) {
  .plan-card-detailed {
    border-radius: 32px;
  }

}

.plan-number {
  color: black;
  opacity: 0.5;
}

.plan-card.not-active {
  background-color: #dedcdc;
}

.indicator {
  position: absolute;
  height: calc(100% - 20px);
}

/* Chip */
.chip {
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 30px;
}

/* Navigation */
.nav {
  background: #09060C;
  color: black;
}

.button-active {
  background: white;
}

/* Back button */
.back-button {
  display: block;
  background: rgba(0, 0, 0, 0.1);
  color: black;
  padding: 16px;
  border-radius: 50%;
  opacity: 0.7;
  transition: all 0.15s;
}

.back-button.small {
  height: 2.5rem;
  width: 2.5rem;
  padding: 0.625rem;
}

.back-button.medium {
  height: 44px;
  width: 44px;
  padding: 0.75rem;
}

.back-button:hover {
  opacity: 1;
}

/* Logo */
.logo {
  width: 65%;
  margin: 0 auto;
  transition: all 0.75s;
}

.logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-wrapper {
  padding-top: 20vh;
  transition: all 0.75s;

  width: 100%;
}

.logo-wrapper.small {
  padding-top: 1vh;
}

.logo-wrapper.small .logo {
  width: 35%;
  height: 60px;
}

/* Input */
.input {
  -webkit-appearance: none;
  background: var(--input-color);
  border-radius: 10px;
  height: 48px;
  width: 100%;
  padding: 0 20px;
  font-weight: 600;
  font-size: 14px;
}

.input.icon-right {
  padding-right: 40px;
}

.input.icon-left {
  padding-left: 40px;
}

.input::placeholder {
  font-weight: normal;
}

.input:focus,
.input:focus {
  outline-color: var(--primary-color);
}

/* Dropdown */
.dropdown-button {
  background: var(--input-color);
  border-radius: 10px;
  height: 48px;
  width: 100%;
  padding: 10px 20px;
  font-weight: 600;
  font-size: 14px;
  text-align: left;
}

.dropdown-button:focus {
  outline-color: var(--primary-color);
}

.dropdown-button.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.dropdown-item.active {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}


/* Link & button*/
.button,
.link-button {
  display: block;
  width: 100%;
  text-align: center;
  padding: 12px 40px;
  text-decoration: none;
  border-radius: 30px;
  transition: all 0.25s;
  font-weight: 600;
  color: black;
  background: #dadada;
}

.button:hover,
.link-button:hover,
.link:hover {
  opacity: 0.8;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button.medium {
  padding: 10px 40px;
  font-size: 14px;
}

.button.small,
.link-button.small {
  padding: 8px 28px;
  font-size: 14px;
  width: auto;
}

.link-button.small {
  width: 100%;
}

.button.full-width {
  width: 100%;
}

.button.narrow,
.link-button.narrow {
  padding: 12px 20px;
}

.button.primary,
.link-button.primary {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.button.secondary,
.link-button.secondary {
  background: var(--secondary-color);
}

.button.secondary.bg-transparent {
  background: rgba(255, 255, 255, 0.9);
}

.button.danger {
  background: var(--danger-color);
}


.button.squared {
  border-radius: 12px;
}

.button.black,
.link-button.black {
  background: black;
  color: white;
}

.button.white,
.link-button.white {
  color: black;
  background: white;
}

.button.gray,
.link-button.gray {
  background: var(--bg-color);
  color: black;
}

.link-button.small .link-arrow {
  top: 10px;
  right: 24px;
}

.link {
  font-size: inherit;
  color: inherit;
  text-decoration: underline;
  font-weight: 600;
}

.link:hover {
  opacity: 0.8;
}

@media (hover: hover) {

  .button.secondary:hover,
  .link-button.secondary:hover {
    opacity: 1;
    background: #E8E8E8;
  }

  .button.white:hover {
    opacity: 1;
    background: #E8E8E8;
  }
}

@media (hover: none) {

  .button.secondary:active,
  .link-button.secondary:active,
  .button.white:active {
    opacity: 1;
    background: #E8E8E8;
  }
}

/* Error message */
.error-message {
  color: var(--danger-color);
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 12px;
}

/* Steps */
.steps {
  opacity: 0;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.steps.visible {
  animation: fade-in 0.5s 0.3s forwards;
}

/* Contact phone */
.contact-phone {
  color: var(--primary-color);
}

/* Select */
.option.selected {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.select.focused {
  outline: 2px solid var(--primary-color);
}

.input.select {
  padding-left: 0.875rem;
}

/* Input validation mark */
.validation.valid {
  color: var(--primary-color);
}

/* Initials */
.initials {
  width: 28px;
  height: 28px;
  background-color: var(--primary-color);
  color: var(--secondary-font-color);
  border-radius: 50%;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  /* padding-top: 5px; */
}


/* Sign up page */
.esim-settings-content {
  overflow: auto;
  margin: -80px -20px;
  padding: 90px 20px 90px;
  height: calc(100dvh - 131px);
}

@media screen and (min-width: 768px) {
  .esim-settings-content {
    height: calc(100dvh - 150px);
  }

}

.signup-agreement {
  color: var(--gray-color);
}

.sim-label {
  background: var(--sign-up-green);
}

.address-notification {
  border: 2px solid var(--sign-up-green);
  background: white;
}

.qr-wrapper {
  border: 4px solid rgba(30, 194, 95, 0.25);
  border-radius: 8px;
}

.address-input {
  height: 36px;
  width: 100%;
  border-bottom: 2px solid transparent;
}

.address-input:focus,
.address-input:focus {
  outline: none;
  border-bottom: 2px solid var(--primary-color);
}

/* Welcome page */
.welcome-content {
  width: 100%;
  padding: 0 12%;
}

.welcome-description {
  text-align: center;
  font-size: 14px;
}

/* Checkbox */
input[type="checkbox"] {
  -webkit-appearance: none;
  appearance: none;

  margin: 0;
  font: inherit;
  color: currentColor;
  width: 15px;
  height: 15px;
  border: 1px solid currentColor;
  border-radius: 2px;

  display: grid;
  place-content: center;
}

input[type="checkbox"]::before {
  content: "";
  display: block;
  border-radius: 15%;
  background-color: var(--gray-color);
  width: 0.45em;
  height: 0.45em;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em var(--form-control-color);
}


input[type="checkbox"]:checked::before {
  transform: scale(1);
}

input[type="checkbox"]:focus {
  outline: max(2px, 0.15em) solid currentColor;
  outline-offset: max(2px, 0.15em);
}

input[type="checkbox"]:disabled {
  --form-control-color: var(--form-control-disabled);

  color: var(--form-control-disabled);
  cursor: not-allowed;
}

/* Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.data-consumed {
  background-color: white;
}


/* Notifications */
.Toastify .Toastify__toast {
  margin: 16px;
  border-radius: 20px;
  padding: 12px;
}

.plan-card-wrapper {
  padding-bottom: 65%;
  height: auto;
  width: 100%;
}

.dashboard-logo-container {
  padding: 4px 0 0 4px;
  margin: auto;
  width: 6.5rem;
  height: 6.5rem;
}

@media screen and (max-width: 420px) {
  .dashboard-logo-container {
    width: 6rem;
    height: 6rem;
  }
}

@media screen and (min-width: 768px) {
  .dashboard-logo-container {
    width: 7rem;
    height: 7rem;
  }
}

/* Intercom */
@media screen and (max-width: 768px) {

  .intercom-lightweight-app-launcher.intercom-launcher,
  .intercom-app .intercom-messenger-frame~div {
    opacity: 0;
    pointer-events: none;
  }
}

/* sim activation */
.sim-activation-logo {
  max-width: 20%;
  height: 100px;
}

.sim-activation-logo .logo {
  width: 100%;
}

/* otp */
.otp input {
  border: 2px solid white;
}

.otp input:focus {
  border: solid 2px var(--primary-color);
  outline: none;
}

.input-code {
  padding: 0 12px;
  font-size: 26px;
  font-weight: 300;
}

.input-code::placeholder {
  font-size: 24px;
  opacity: 0.5;
}


/* terms */
.plain-text .list-title {
  margin-bottom: 8px;
  margin-top: 16px;
  font-weight: 600;
}

.plain-text .list-decimal {
  list-style-type: decimal;
  margin-left: 16px;
}

.plain-text .big-list-margin li {
  margin-bottom: 16px;
}

.plain-text .list-alpha {
  list-style-type: lower-alpha;
  margin-left: 16px;
}

.plain-text .small-list-margin li {
  margin-bottom: 8px;
}

.plain-text .list-roman {
  list-style-type: lower-roman;
  margin-left: 16px;
}

.plain-text .list-disc {
  list-style-type: disc;
  margin-left: 16px;
}

.plain-text .list-circle {
  list-style-type: circle;
  margin-left: 16px;
}

.plain-text .paragraphs p {
  margin-bottom: 12px;
}

.plain-text .paragraphs .bold {
  font-weight: 600;
}