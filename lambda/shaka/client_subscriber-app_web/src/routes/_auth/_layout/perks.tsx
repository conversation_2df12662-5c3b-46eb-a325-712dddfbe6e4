import { Tab } from '@headlessui/react';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import FullPageLoader from 'src/components/common/FullPageLoader';
import PageTitle from 'src/components/common/PageTitle';
import PerkListItem from 'src/components/perks/PerkListItem';
import { formatNumber } from 'src/helpers';
import usePerks from 'src/hooks/usePerks';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Slider from 'react-slick';
import PerkDetails, { ClaimUnit } from 'src/components/perks/PerkDetails';
import Drawer from 'src/components/perks/Drawer';
import { ROUTES } from 'src/config/routes';

import { useRequest } from 'src/hooks/useRequest';
import { claimPerk } from 'src/api/perks';
import { useClient } from 'src/hooks/useClient';
import { TConductorInstance } from 'react-canvas-confetti/dist/types';
import Pride from 'react-canvas-confetti/dist/presets/pride';
import { getConfettiOptions } from 'src/config/confetti-animation';
import { twMerge } from 'tailwind-merge';

export const Route = createFileRoute('/_auth/_layout/perks')({
  component: Perks
});

const hidePerksBalance = window.clientConfig.hidePerksBalance;

function Perks() {
  const urlParams = new URLSearchParams(window.location.search);
  const perkId = Number(urlParams.get('perk'));

  const { pointsNaming } = useClient();
  const {
    perks,
    activePerks,
    claimedPerks,
    pointsBalance,
    isLoading,
    activePerksAmount,
    setPerks
  } = usePerks();

  const { run: runClaimPerk } = useRequest(claimPerk, {
    withToast: true,
    successMessage: 'Perk claimed successfully'
  });
  const navigate = useNavigate();
  const sliderRef = useRef<Slider | null>(null);

  const tabs = [
    `Perks (${activePerksAmount})`,
    `Claimed Perks (${claimedPerks.length})`
  ];

  const [activeTab, setActiveTab] = useState(0);
  const [currentActivePerks, setCurrentActivePerks] = useState(activePerks);
  const [indicator, setIndicator] = useState({
    width: '50%',
    position: '0'
  });

  const tabRef = useRef<HTMLDivElement>(null);

  const selectedPerk = perks.find((perk) => perk.id === perkId);

  const settings = {
    dots: false,
    infinite: false,
    speed: 250,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false
  };

  const [conductor, setConductor] = useState<TConductorInstance>();

  const onInitConfetti = ({ conductor }: { conductor: TConductorInstance }) => {
    setConductor(conductor);
  };

  const onShootHandler = () => {
    conductor?.shoot();
  };

  const onClaim = (claimUnit: ClaimUnit) => {
    if (!selectedPerk) {
      return;
    }

    runClaimPerk({
      id: selectedPerk.id,
      claim_with_points: claimUnit === 'points'
    })
      .then((res) => {
        setPerks(res);
        const updatedPerk = res.perks.find(
          (perk) => perk.id === selectedPerk.id
        );
        setCurrentActivePerks(
          currentActivePerks.map((p) =>
            p.id === updatedPerk?.id ? updatedPerk : p
          )
        );
        onShootHandler();
      })
      .catch((err) => {
        const isOutOfStock =
          err?.response?.data?.error?.includes('out of stock');
        if (isOutOfStock) {
          sliderRef.current?.slickPrev();
        }
      });
  };

  const onTabChange = (index: number) => {
    setActiveTab(index);
    tabRef.current?.scrollTo({ top: 0 });
  };

  const returnToPerks = () => {
    navigate({ to: ROUTES.Perks });
    setTimeout(() => {
      setCurrentActivePerks(activePerks);
    }, 400);
  };

  useEffect(() => {
    const activeTabElement = document.getElementById(`perk-tab-${activeTab}`);
    setIndicator({
      width: `${activeTabElement?.offsetWidth}px`,
      position: `${activeTabElement?.offsetLeft}px`
    });
    setCurrentActivePerks(activePerks);
  }, [activeTab, isLoading]);

  if (isLoading) {
    return <FullPageLoader />;
  }

  return (
    <div className="flex h-full flex-col -mb-28">
      <PageTitle uppercase withBottomMargin={false}>
        Perks
      </PageTitle>
      {!hidePerksBalance && (
        <div className="bg-white rounded-2xl p-4 py-5 mt-5">
          <h3 className="text-xl font-semibold">
            {formatNumber(pointsBalance)} {pointsNaming.plural}
          </h3>
          <p className="text-xs">current reward balance</p>
        </div>
      )}
      <div
        className={twMerge(
          'relative h-[calc(100dvh_-_175px)] pt-1',
          hidePerksBalance && 'h-[calc(100dvh_-_69px)]'
        )}
      >
        <Tab.Group
          onChange={onTabChange}
          defaultIndex={0}
          selectedIndex={activeTab}
        >
          <Tab.List className="absolute backdrop-blur-md top-0 -inset-x-5 px-5 pt-10 pb-3 bg-gradient-to-t from-[#F3F3F3]/70 to-[#F3F3F3] max-sm:w-screen">
            <div className="relative">
              {tabs.map((tab, index) => (
                <Tab
                  key={tab}
                  className={'focus:outline-none'}
                  id={`perk-tab-${index}`}
                >
                  {({ selected }) => (
                    <span
                      className={clsx(
                        'inline-block text-sm py-1.5 px-4 transition-all',
                        !selected && 'text-black/60'
                      )}
                    >
                      {tab}
                    </span>
                  )}
                </Tab>
              ))}
              <div
                className="rounded-full bg-[#E1E1E1] absolute top-0 transition-all h-full -z-10"
                style={{
                  left: indicator.position,
                  width: indicator.width
                }}
              />
            </div>
          </Tab.List>
          <Tab.Panels
            className="h-full overflow-y-auto pb-28 pt-[86px] no-scrollbar"
            ref={tabRef}
          >
            <Tab.Panel>
              {currentActivePerks.length ? (
                <>
                  {currentActivePerks.map((perk) => (
                    <PerkListItem
                      key={perk.id}
                      perk={perk}
                      canClaimByPoints={Number(perk.amount) <= pointsBalance}
                    />
                  ))}
                  <Drawer
                    isOpen={activeTab === 0 && Boolean(perkId)}
                    onClose={returnToPerks}
                  >
                    {/* <button onClick={onShootHandler}>test</button> */}
                    <Slider
                      {...settings}
                      ref={sliderRef}
                      initialSlide={currentActivePerks.findIndex(
                        (perk) => perk.id === perkId
                      )}
                      beforeChange={(_: number, next: number) =>
                        navigate({
                          search: { perk: currentActivePerks[next].id }
                        })
                      }
                    >
                      {currentActivePerks.map((perk) => (
                        <PerkDetails
                          key={perk.id}
                          perk={perk}
                          onClaim={onClaim}
                          canClaimByPoints={
                            Number(perk.amount) <= pointsBalance
                          }
                        />
                      ))}
                    </Slider>
                    <div className="absolute inset-0 pointer-events-none z-30">
                      <Pride
                        onInit={onInitConfetti}
                        globalOptions={{ resize: true }}
                        decorateOptions={getConfettiOptions()}
                        style={{ width: '100%', height: '100%' }}
                      />
                    </div>
                  </Drawer>
                </>
              ) : (
                <p className="text-center mt-20">No perks available</p>
              )}
            </Tab.Panel>
            <Tab.Panel>
              {claimedPerks.length ? (
                <>
                  {claimedPerks.map((perk) => (
                    <PerkListItem
                      key={perk.id}
                      perk={perk}
                      canClaimByPoints={Number(perk.amount) <= pointsBalance}
                    />
                  ))}
                  <Drawer
                    isOpen={activeTab === 1 && Boolean(perkId)}
                    onClose={returnToPerks}
                  >
                    <Slider
                      {...settings}
                      initialSlide={claimedPerks.findIndex(
                        (perk) => perk.id === perkId
                      )}
                      beforeChange={(_: number, next: number) =>
                        navigate({ search: { perk: claimedPerks[next].id } })
                      }
                    >
                      {claimedPerks.map((perk) => (
                        <PerkDetails
                          key={perk.id}
                          perk={perk}
                          onClaim={onClaim}
                          canClaimByPoints={
                            Number(perk.amount) <= pointsBalance
                          }
                        />
                      ))}
                    </Slider>
                  </Drawer>
                </>
              ) : (
                <p className="text-center mt-20">No claimed perks</p>
              )}
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
}
