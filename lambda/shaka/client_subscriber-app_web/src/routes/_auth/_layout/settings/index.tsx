import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import useSubscription from 'src/hooks/useSubscription';
import { getInitials } from 'src/helpers';
import { useMemo, useState } from 'react';
import { useAuth } from 'src/hooks/useAuth';
import PageTitle from 'src/components/common/PageTitle';
import SettingLink, {
  settingsLinkStyles
} from 'src/components/settings/SettingsLink';
import SubscriptionActionLink from 'src/components/settings/SubscriptionActionLink';
import SupportLink from 'src/components/settings/SupportLink';
import SettingsBlock from 'src/components/settings/SettingsBlock';
import { PlanChangeStatus, PlanChangeType } from 'src/types/subscriber';
import {
  otherLinks,
  subscriberLinks,
  subscriptionLinks,
  supportLinks
} from 'src/config/settings-buttons';
import { LogoutIcon } from 'src/assets/icons';
import PlanSettingsCard from 'src/components/PlanSettingsCard';
import NotificationDialog from 'src/components/common/NotificationDialog';
import BoltOnsDrawer from 'src/components/bolt-ons';
import BoltonPaymentResult from 'src/components/bolt-ons/BoltOnPaymentResult';
import PlanUpgradeDrawer from 'src/components/plan-upgrade';
import useBoltOns from 'src/hooks/useBoltOns';

const Settings = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  const { subscriber, setSubscriber, subscriberLastPlan } = useSubscription();
  const { boltOns } = useBoltOns();

  const searchParams = new URLSearchParams(window.location.search);
  const planChangeStep = searchParams.get('plan-change');
  const modal = searchParams.get('modal');
  const isBoltOnsOpen = modal === 'bolt-ons';
  const isBoltOnsPaymentOpen = modal === 'bolt-ons-payment';
  const isBoltOnsDisabled =
    (boltOns?.length === 0 && !subscriberLastPlan.roaming_bolt_on_eu) ||
    subscriberLastPlan.sim_activation_status !== 'active';

  const isPlanChanging =
    subscriber?.plans?.[0].latest_plan_change?.status ===
      PlanChangeStatus.IN_PROGRESS &&
    subscriber?.plans?.[0].latest_plan_change.change_type !==
      PlanChangeType.CANCEL_CHANGE;

  const [isLogOutConfirmation, setIsLogOutConfirmation] = useState(false);

  const closeConfirmation = () => setIsLogOutConfirmation(false);

  const handleLogOut = () => {
    setSubscriber(null);
    auth.logout();
    navigate({ to: ROUTES.Login });
  };

  const handleBoltOnsClose = () => {
    setTimeout(() => {
      navigate({ to: '/settings' });
    }, 300);
  };

  const subscriptionActionButtons = useMemo(() => {
    return subscriptionLinks.map(({ to, label, disabled, search, id }) => (
      <SubscriptionActionLink
        to={to}
        search={search}
        disabled={
          disabled ||
          (to === ROUTES.Settings && isPlanChanging) ||
          (id === 'bolt-ons' && isBoltOnsDisabled)
        }
        key={label}
      >
        {label}
      </SubscriptionActionLink>
    ));
  }, [isBoltOnsDisabled, isPlanChanging]);

  return (
    <>
      <PageTitle withBottomMargin={false}>manage</PageTitle>
      {planChangeStep && <PlanUpgradeDrawer step={Number(planChangeStep)} />}

      {isBoltOnsOpen && (
        <BoltOnsDrawer isOpen onClose={handleBoltOnsClose} title="BoltOns" />
      )}
      {isBoltOnsPaymentOpen && <BoltonPaymentResult />}

      <p className="my-4 text-center text-xs">1 plan</p>

      <PlanSettingsCard />

      <div className="flex justify-center gap-4 mt-4 mb-8">
        {subscriptionActionButtons}
      </div>

      <SettingsBlock title="Support">
        {supportLinks.map(({ to, label, Icon }) => (
          <SupportLink to={to} key={label}>
            <Icon className="h-3 w-3" />
            {label}
          </SupportLink>
        ))}
      </SettingsBlock>

      <SettingsBlock title={subscriber?.name || 'User data'}>
        {subscriber?.name && (
          <span className="uppercase initials">
            {getInitials(subscriber?.name)}
          </span>
        )}
        {subscriberLinks.map(({ to, label, Icon }) => (
          <SupportLink to={to} key={label}>
            <Icon className="h-3 w-3" />
            {label}
          </SupportLink>
        ))}
      </SettingsBlock>

      <SettingsBlock title="Other">
        <div className="w-full flex flex-col bg-white rounded-xl divide-y divide-black/5">
          {otherLinks.map(({ to, label, disabled, Icon, blank }) => (
            <SettingLink to={to} key={label} disabled={disabled} blank={blank}>
              <Icon className="h-4 w-4" />
              {label}
            </SettingLink>
          ))}
          <button
            onClick={() => setIsLogOutConfirmation(true)}
            className={settingsLinkStyles}
          >
            <LogoutIcon className="h-4 w-4" />
            Sign out
          </button>
        </div>
      </SettingsBlock>

      <NotificationDialog
        isOpen={isLogOutConfirmation}
        onCancel={closeConfirmation}
        onSubmit={handleLogOut}
        title="Sign out"
        description="Are you sure you wish to sign out?"
        cancelActionColor="default"
        submitActionColor="default"
      />
    </>
  );
};

export const Route = createFileRoute('/_auth/_layout/settings/')({
  component: Settings
});
