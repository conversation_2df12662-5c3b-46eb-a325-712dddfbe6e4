import { useCallback, useEffect, useState } from 'react';
import useSubscription from './useSubscription';
import {
  getSimSettingsSelectedType,
  getStripeSessionExpiresAt,
  getStripeSessionId
} from 'src/config/localStorageActions';
import { useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { isPhysicalSimDisabled } from 'src/config/env-vars';

export default function useSubscriptionRedirect() {
  const { subscriber, isSubscriptionLoaded, subscriberLastPlan } =
    useSubscription();
  const navigate = useNavigate();

  const [isSessionExpired, setIsSessionExpired] = useState(false);

  const isEsimSubscriber = subscriberLastPlan.sim_type === 'esim';
  const isSimSettingsInProgress = Boolean(getSimSettingsSelectedType());

  const checkSession = useCallback(async () => {
    const sessionId = getStripeSessionId();
    const sessionExpiresAt = getStripeSessionExpiresAt();
    const isSessionActive = Boolean(
      sessionExpiresAt && new Date(Number(sessionExpiresAt)) > new Date()
    );

    setIsSessionExpired(!sessionId || !isSessionActive);
  }, []);

  useEffect(() => {
    const shouldRedirectToAddress =
      !subscriber?.address &&
      !isEsimSubscriber &&
      getSimSettingsSelectedType() != 'self-serve' &&
      !isPhysicalSimDisabled;

    if (isSubscriptionLoaded && subscriber) {
      if (subscriber?.address) {
        return;
      }

      if (!subscriber.is_verified) {
        navigate({ to: ROUTES.EmailConfirmation });
        return;
      }

      if (!subscriber?.plans && !subscriber?.phone_number) {
        navigate({ to: ROUTES.ExplorePlans });
        return;
      }

      if (isSimSettingsInProgress) {
        navigate({ to: ROUTES.ChooseSim });
        return;
      }

      if (!subscriber?.plans) {
        if (shouldRedirectToAddress) {
          navigate({ to: ROUTES.AddressDetails });
          return;
        }

        navigate({ to: ROUTES.ExplorePlans });
        return;
      } else {
        if (!subscriber?.plans[0].self_activated && shouldRedirectToAddress) {
          navigate({ to: ROUTES.AddressDetails });
          return;
        }
      }
    }
  }, [
    isSessionExpired,
    subscriber,
    isSubscriptionLoaded,
    navigate,
    isEsimSubscriber,
    isSimSettingsInProgress
  ]);

  useEffect(() => {
    checkSession();
  }, [checkSession]);
}
